# 一致性脱敏技术说明

## 🔒 核心问题解决

### 问题：脱敏后如何确保两条数据是否相同？

**传统脱敏方法的问题**：
- 使用星号掩码（如：张三 → 张*）无法区分不同的"张*"
- 随机脱敏每次产生不同结果，无法识别相同患者
- 同时保存原始数据和脱敏数据存在泄露风险

**一致性脱敏解决方案**：
- ✅ 相同输入 → 相同输出（确定性）
- ✅ 不同输入 → 不同输出（唯一性）
- ✅ 不可逆推导（安全性）
- ✅ 保持数据关联性（实用性）

## 🛡️ 技术原理

### 1. 哈希算法确保一致性
```python
def _generate_consistent_hash(self, data: str) -> str:
    """生成一致性哈希"""
    combined = f"{secret_key}:{data}"
    return hashlib.sha256(combined.encode('utf-8')).hexdigest()
```

### 2. 基于哈希的脱敏映射
```python
# 姓名脱敏示例
hash_value = self._generate_consistent_hash("张三")
hash_int = int(hash_value[:8], 16)
surname_idx = hash_int % len(surnames)  # 选择姓氏
given_name_idx = (hash_int >> 8) % len(given_names)  # 选择名字
masked_name = f"{surnames[surname_idx]}{given_names[given_name_idx]}"
```

## 📊 脱敏效果演示

### 姓名脱敏
| 原始姓名 | 脱敏姓名 | 一致性验证 |
|---------|---------|-----------|
| 张三    | 周慧    | ✅ 多次运行结果相同 |
| 李四    | 杨勇    | ✅ 多次运行结果相同 |
| 张三    | 周慧    | ✅ 相同输入产生相同输出 |
| 王五    | 杨英    | ✅ 不同输入产生不同输出 |

### 患者ID脱敏
| 原始ID | 脱敏ID | 一致性验证 |
|-------|-------|-----------|
| P1001 | P1792 | ✅ 多次运行结果相同 |
| P1002 | P4306 | ✅ 多次运行结果相同 |
| P1001 | P1792 | ✅ 相同输入产生相同输出 |

### 电话脱敏
| 原始电话 | 脱敏电话 | 一致性验证 |
|---------|---------|-----------|
| 13812345678 | 138****5432 | ✅ 基于哈希生成后四位 |
| 13987654321 | 138****8765 | ✅ 不同号码不同后缀 |

## 🔧 使用方法

### 1. 生成测试数据
```bash
python generate_test_data.py
```

### 2. 验证一致性
脚本会生成两个文件：
- `test_data_original.xlsx` - 原始数据
- `test_data_masked.xlsx` - 脱敏数据

### 3. 一致性验证结果
```
🔍 一致性验证：
✅ 姓名 '张三' 的 13 条记录都脱敏为 '周慧'
✅ 姓名 '李四' 的 8 条记录都脱敏为 '杨勇'
✅ 姓名 '王五' 的 6 条记录都脱敏为 '杨英'
```

## 🎯 核心优势

### 1. **数据一致性**
- 相同患者的多次就诊记录保持关联
- 可以正确统计患者的手术次数
- 支持患者历史数据分析

### 2. **隐私保护**
- 使用SHA-256哈希算法，不可逆
- 密钥保护，增加破解难度
- 完全删除原始敏感数据

### 3. **业务可用性**
- 保持数据的统计特性
- 支持正常的业务查询和分析
- 不影响现有业务逻辑

### 4. **合规安全**
- 符合数据保护法规要求
- 降低数据泄露风险
- 支持审计和追踪

## 🔐 安全特性

### 1. **密钥保护**
```python
secret_key = "anesthesia_qc_secure_2024"  # 可配置的密钥
```

### 2. **哈希不可逆**
- 使用SHA-256算法
- 即使知道脱敏结果也无法推导原始数据
- 彩虹表攻击防护（加盐）

### 3. **数据最小化**
- 只保存脱敏后的数据
- 原始敏感数据完全删除
- 零泄露风险

## 📈 性能特点

### 1. **缓存机制**
- 相同数据只计算一次哈希
- 内存缓存提高处理速度
- 批量处理优化

### 2. **处理速度**
- 哈希计算快速
- 支持大批量数据处理
- 实时脱敏能力

## 🚀 实际应用

### 1. **数据导入时脱敏**
```python
# 导入时自动使用一致性脱敏
patient_data = {
    'patient_id': masker.mask_hospital_id_consistent(original_id),
    'name': masker.mask_name_consistent(original_name),
    'phone': masker.mask_phone_number_consistent(original_phone)
}
```

### 2. **数据库迁移**
```bash
# 将现有数据迁移到安全脱敏模式
python src/database/migrate_to_secure_schema.py
```

### 3. **业务查询**
```sql
-- 查询某个脱敏患者的所有手术记录
SELECT * FROM surgery_records sr
JOIN patients p ON sr.patient_id = p.id
WHERE p.name = '周慧';  -- 脱敏后的姓名
```

## ⚠️ 重要提醒

1. **不可逆操作**：一旦使用一致性脱敏，原始数据将被永久删除
2. **密钥管理**：密钥丢失将导致无法生成一致的脱敏结果
3. **备份建议**：迁移前请确保已备份重要数据
4. **测试验证**：建议先在测试环境验证脱敏效果

## 📞 技术支持

如有问题，请参考：
1. 运行 `python generate_test_data.py` 查看演示
2. 检查生成的Excel文件验证一致性
3. 查看系统日志了解脱敏处理过程
