{% extends "base.html" %}

{% block title %}数据导入 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}数据导入{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Excel文件导入
                </h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="fileInput" name="file" accept=".xlsx,.xls" required>
                        <div class="form-text">支持 .xlsx 和 .xls 格式，最大 50MB</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enablePrivacy" name="enable_privacy" checked>
                            <label class="form-check-label" for="enablePrivacy">
                                启用数据脱敏（推荐）
                            </label>
                        </div>
                        <div class="form-text">脱敏后将隐藏患者姓名、身份证号等敏感信息</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary" id="previewBtn">
                            <i class="fas fa-eye me-2"></i>
                            预览数据
                        </button>
                        <button type="submit" class="btn btn-primary" id="uploadBtn" disabled>
                            <i class="fas fa-upload me-2"></i>
                            开始导入
                        </button>
                    </div>
                </form>
                
                <!-- 进度显示 -->
                <div id="progressContainer" class="mt-4" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备导入...</small>
                    </div>
                </div>
                
                <!-- 预览结果 -->
                <div id="previewContainer" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-table me-2"></i>数据预览</h6>
                        </div>
                        <div class="card-body">
                            <div id="previewContent"></div>
                        </div>
                    </div>
                </div>

                <!-- 结果显示 -->
                <div id="resultContainer" class="mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>导入完成</h6>
                        <div id="resultSummary"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入历史 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    最近导入记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>导入时间</th>
                                <th>记录数</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <tr>
                                <td colspan="5" class="text-center text-muted">暂无导入记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const progressContainer = document.getElementById('progressContainer');
    const resultContainer = document.getElementById('resultContainer');
    const previewContainer = document.getElementById('previewContainer');
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');
    const uploadBtn = document.getElementById('uploadBtn');
    const previewBtn = document.getElementById('previewBtn');

    let selectedFile = null;

    // 加载导入历史
    loadImportHistory();

    // 文件选择事件
    document.getElementById('fileInput').addEventListener('change', function(e) {
        selectedFile = e.target.files[0];
        if (selectedFile) {
            previewBtn.disabled = false;
            uploadBtn.disabled = true; // 需要先预览才能导入
            previewContainer.style.display = 'none';
            resultContainer.style.display = 'none';
        } else {
            previewBtn.disabled = true;
            uploadBtn.disabled = true;
        }
    });

    // 预览按钮事件
    previewBtn.addEventListener('click', function() {
        if (!selectedFile) {
            showAlert('请先选择文件', 'warning');
            return;
        }
        previewData();
    });

    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!selectedFile) {
            showAlert('请先选择文件', 'warning');
            return;
        }

        if (uploadBtn.disabled) {
            showAlert('请先预览数据再导入', 'warning');
            return;
        }

        uploadFile(selectedFile);
    });

    function previewData() {
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('max_rows', '10'); // 只预览前10行

        // 显示加载状态
        previewContainer.style.display = 'block';
        document.getElementById('previewContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在预览数据...</p>
            </div>
        `;

        fetch('/api/preview-data', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPreviewResults(data);
                uploadBtn.disabled = false; // 预览成功后允许导入
            } else {
                throw new Error(data.error || '预览失败');
            }
        })
        .catch(error => {
            showAlert('预览失败: ' + error.message, 'danger');
            previewContainer.style.display = 'none';
        });
    }

    function showPreviewResults(data) {
        const previewContent = document.getElementById('previewContent');
        const fileInfo = data.file_info;
        const previewData = data.preview_data;

        let html = `
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="card border-info text-center">
                        <div class="card-body">
                            <i class="fas fa-file-excel fa-2x text-info mb-2"></i>
                            <h6>总行数</h6>
                            <h4 class="text-info">${fileInfo.total_rows}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success text-center">
                        <div class="card-body">
                            <i class="fas fa-columns fa-2x text-success mb-2"></i>
                            <h6>列数</h6>
                            <h4 class="text-success">${fileInfo.total_columns}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning text-center">
                        <div class="card-body">
                            <i class="fas fa-file-alt fa-2x text-warning mb-2"></i>
                            <h6>文件大小</h6>
                            <h4 class="text-warning">${formatFileSize(selectedFile.size)}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-primary text-center">
                        <div class="card-body">
                            <i class="fas fa-table fa-2x text-primary mb-2"></i>
                            <h6>工作表</h6>
                            <h4 class="text-primary">${fileInfo.sheet_name}</h4>
                        </div>
                    </div>
                </div>
            </div>
        `;

        if (previewData.length > 0) {
            const columns = Object.keys(previewData[0]);
            html += `
                <h6><i class="fas fa-table me-1"></i> 数据预览 (前${previewData.length}行)</h6>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-sm">
                        <thead class="table-dark">
                            <tr>
                                ${columns.map(col => `<th>${col}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${previewData.map(row => `
                                <tr>
                                    ${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        previewContent.innerHTML = html;
    }

    function uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('enable_privacy', document.getElementById('enablePrivacy').checked);

        // 显示进度
        progressContainer.style.display = 'block';
        resultContainer.style.display = 'none';
        uploadBtn.disabled = true;

        progressText.textContent = '正在上传文件...';
        progressBar.style.width = '10%';

        fetch('/api/import-data', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                progressBar.style.width = '100%';
                progressText.textContent = '导入完成';

                // 显示结果
                showResult(data);

                // 重新加载历史记录
                loadImportHistory();

                // 重置表单
                uploadForm.reset();
            } else {
                throw new Error(data.error || '导入失败');
            }
        })
        .catch(error => {
            showAlert('导入失败: ' + error.message, 'danger');
            progressContainer.style.display = 'none';
        })
        .finally(() => {
            uploadBtn.disabled = false;
        });
    }

    function showResult(data) {
        const resultSummary = document.getElementById('resultSummary');
        resultSummary.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <strong>文件名:</strong> ${data.filename}
                </div>
                <div class="col-md-3">
                    <strong>总记录数:</strong> ${data.total_records}
                </div>
                <div class="col-md-3">
                    <strong>新增记录:</strong> ${data.new_records}
                </div>
                <div class="col-md-3">
                    <strong>重复记录:</strong> ${data.duplicate_records}
                </div>
            </div>
        `;

        resultContainer.style.display = 'block';
        progressContainer.style.display = 'none';
    }

    function loadImportHistory() {
        fetch('/api/import-history')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('historyTableBody');

            if (data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无导入记录</td></tr>';
                return;
            }

            tbody.innerHTML = data.map(record => `
                <tr>
                    <td>${record.filename}</td>
                    <td>${new Date(record.created_at).toLocaleString()}</td>
                    <td>${record.total_records}</td>
                    <td>
                        <span class="badge bg-${record.status === 'completed' ? 'success' : 'danger'}">
                            ${record.status === 'completed' ? '成功' : '失败'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-info" onclick="viewDetails('${record.id}')">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                </tr>
            `).join('');
        })
        .catch(error => {
            console.error('加载导入历史失败:', error);
        });
    }

    window.viewDetails = function(batchId) {
        // 这里可以实现查看详情的功能
        showAlert('详情功能开发中', 'info');
    };

    // 文件大小格式化函数
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>
{% endblock %}
