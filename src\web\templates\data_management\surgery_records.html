{% extends "base.html" %}

{% block title %}手术记录管理 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item"><a href="/data-management">数据管理</a></li>
            <li class="breadcrumb-item active" aria-current="page">手术记录</li>
        </ol>
    </nav>
{% endblock %}

{% block content %}
<!-- 工具栏 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" id="addRecordBtn">
                                <i class="fas fa-plus me-2"></i>新增记录
                            </button>
                            <button class="btn btn-success" id="batchEditBtn" disabled>
                                <i class="fas fa-edit me-2"></i>批量编辑
                            </button>
                            <button class="btn btn-warning" id="exportBtn">
                                <i class="fas fa-download me-2"></i>导出数据
                            </button>
                            <button class="btn btn-danger" id="batchDeleteBtn" disabled>
                                <i class="fas fa-trash me-2"></i>批量删除
                            </button>
                            <button class="btn btn-info" id="refreshBtn" title="刷新数据">
                                <i class="fas fa-sync-alt me-2"></i>刷新
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索患者姓名、手术类型、麻醉医师...">
                            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="btn btn-outline-secondary" type="button" id="filterBtn">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选面板 -->
<div class="row mb-3" id="filterPanel" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-filter me-2"></i>高级筛选</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">手术日期</label>
                        <div class="d-flex gap-2">
                            <input type="date" class="form-control" id="startDate">
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">麻醉方法</label>
                        <select class="form-select" id="anesthesiaMethod">
                            <option value="">全部</option>
                            <option value="全身麻醉">全身麻醉</option>
                            <option value="椎管内麻醉">椎管内麻醉</option>
                            <option value="神经阻滞">神经阻滞</option>
                            <option value="局部麻醉">局部麻醉</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">科室</label>
                        <select class="form-select" id="department">
                            <option value="">全部</option>
                            <option value="普外科">普外科</option>
                            <option value="骨科">骨科</option>
                            <option value="心胸外科">心胸外科</option>
                            <option value="神经外科">神经外科</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ASA分级</label>
                        <select class="form-select" id="asaGrade">
                            <option value="">全部</option>
                            <option value="I">I级</option>
                            <option value="II">II级</option>
                            <option value="III">III级</option>
                            <option value="IV">IV级</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button class="btn btn-primary" id="applyFilterBtn">
                            <i class="fas fa-search me-2"></i>应用筛选
                        </button>
                        <button class="btn btn-secondary" id="resetFilterBtn">
                            <i class="fas fa-undo me-2"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-table me-2"></i>手术记录列表
                    <span class="badge bg-primary ms-2" id="totalCount">0</span>
                </h6>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll">
                    <label class="form-check-label" for="selectAll">
                        全选
                    </label>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAllHeader" class="form-check-input">
                                </th>
                                <th class="sortable" data-sort="surgery_date">
                                    手术日期 <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="patient_name">
                                    患者姓名 <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="admission_number">
                                    住院号 <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="anesthesia_method">
                                    麻醉方法 <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="surgery_type">
                                    手术类型 <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-sort="department">
                                    科室 <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th width="120">操作</th>
                            </tr>
                        </thead>
                        <tbody id="recordsTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 mb-0">正在加载数据...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center gap-2">
                            <span>每页显示:</span>
                            <select class="form-select form-select-sm" id="pageSize" style="width: auto;">
                                <option value="10">10</option>
                                <option value="25" selected>25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>条记录</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <nav aria-label="分页导航">
                            <ul class="pagination pagination-sm justify-content-end mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sort-icon {
    opacity: 0.6;
    transition: all 0.2s ease;
    font-size: 0.8em;
}

.sortable:hover .sort-icon {
    opacity: 1;
}

.sortable.sort-asc .sort-icon::before {
    content: "\f0de"; /* fa-sort-up */
    color: #28a745;
}

.sortable.sort-desc .sort-icon::before {
    content: "\f0dd"; /* fa-sort-down */
    color: #dc3545;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.selected-row {
    background-color: rgba(0, 123, 255, 0.2) !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = 1;
    let pageSize = 25;
    let sortField = 'surgery_date';
    let sortOrder = 'desc';
    let selectedRecords = new Set();

    // 初始化
    loadRecords();
    initializeEventListeners();

    function initializeEventListeners() {
        // 搜索功能
        document.getElementById('searchBtn').addEventListener('click', performSearch);
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') performSearch();
        });

        // 筛选功能
        document.getElementById('filterBtn').addEventListener('click', toggleFilterPanel);
        document.getElementById('applyFilterBtn').addEventListener('click', applyFilters);
        document.getElementById('resetFilterBtn').addEventListener('click', resetFilters);

        // 分页
        document.getElementById('pageSize').addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1;
            loadRecords();
        });

        // 排序
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const field = this.dataset.sort;
                if (sortField === field) {
                    sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
                } else {
                    sortField = field;
                    sortOrder = 'asc';
                }
                updateSortIcons();
                loadRecords();
            });
        });

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', toggleSelectAll);
        document.getElementById('selectAllHeader').addEventListener('change', toggleSelectAll);

        // 批量操作
        document.getElementById('batchEditBtn').addEventListener('click', batchEdit);
        document.getElementById('batchDeleteBtn').addEventListener('click', batchDelete);
        document.getElementById('exportBtn').addEventListener('click', exportData);
        document.getElementById('addRecordBtn').addEventListener('click', addRecord);
        document.getElementById('refreshBtn').addEventListener('click', function() {
            console.log('🔄 用户点击刷新按钮');
            showAlert('正在刷新数据...', 'info');
            loadRecords();
        });
    }

    function loadRecords() {
        console.log('🔄 loadRecords 函数被调用，当前页:', currentPage);

        const params = new URLSearchParams({
            page: currentPage,
            page_size: pageSize,
            sort_field: sortField,
            sort_order: sortOrder,
            search: document.getElementById('searchInput').value || '',
            start_date: document.getElementById('startDate').value || '',
            end_date: document.getElementById('endDate').value || '',
            anesthesia_method: document.getElementById('anesthesiaMethod').value || '',
            department: document.getElementById('department').value || '',
            asa_grade: document.getElementById('asaGrade').value || ''
        });

        console.log('📡 发送API请求:', `/api/surgery-records?${params}`);

        fetch(`/api/surgery-records?${params}`, {
            // 禁用缓存，确保获取最新数据
            cache: 'no-cache',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
        .then(response => {
            console.log('📥 收到API响应，状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📊 API响应数据:', data); // 调试信息

            if (data.success) {
                console.log('✅ 数据加载成功，记录数量:', data.records.length);
                console.log('📋 记录列表:', data.records.map(r => `ID:${r.id}`).join(', '));

                // 适配实际的API结构
                renderRecords(data.records);
                renderPagination({
                    current_page: data.pagination.current_page,
                    total_pages: data.pagination.total_pages,
                    total: data.pagination.total_records,
                    has_prev: data.pagination.has_prev,
                    has_next: data.pagination.has_next
                });

                // 更新总数显示
                const totalCountElement = document.getElementById('totalCount');
                if (totalCountElement) {
                    totalCountElement.textContent = data.pagination.total_records;
                    console.log('📊 更新总数显示:', data.pagination.total_records);
                }
            } else {
                console.error('❌ API返回错误:', data.error);
                showAlert('加载数据失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('❌ API请求异常:', error); // 调试信息
            showAlert('加载数据失败: ' + error.message, 'danger');
        });
    }

    function renderRecords(records) {
        console.log('renderRecords调用，参数:', records); // 调试信息

        const tbody = document.getElementById('recordsTableBody');

        // 检查records是否为数组
        if (!Array.isArray(records)) {
            console.error('records不是数组:', typeof records, records);
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4 text-danger">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <p>数据格式错误</p>
                    </td>
                </tr>
            `;
            return;
        }

        if (records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无数据</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = records.map(record => `
            <tr class="${selectedRecords.has(record.id) ? 'selected-row' : ''}" data-id="${record.id}">
                <td>
                    <input type="checkbox" class="form-check-input record-checkbox"
                           value="${record.id}" ${selectedRecords.has(record.id) ? 'checked' : ''}>
                </td>
                <td>${record.surgery_date}</td>
                <td>${record.patient_name || '未知'}</td>
                <td>${record.admission_number || '未知'}</td>
                <td>
                    <span class="badge bg-primary">${record.anesthesia_method || '未知'}</span>
                </td>
                <td class="text-truncate" style="max-width: 200px;" title="${record.surgery_type || '未知'}">${record.surgery_type || '未知'}</td>
                <td>${record.department || '未知'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewRecord(${record.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="editRecord(${record.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteRecord(${record.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // 重新绑定复选框事件
        document.querySelectorAll('.record-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const recordId = parseInt(this.value);
                const row = this.closest('tr');

                if (this.checked) {
                    selectedRecords.add(recordId);
                    row.classList.add('selected-row');
                } else {
                    selectedRecords.delete(recordId);
                    row.classList.remove('selected-row');
                }

                updateBatchButtons();
                updateSelectAllState();
            });
        });
    }

    function renderPagination(pagination) {
        const paginationEl = document.getElementById('pagination');
        const { current_page, total_pages, has_prev, has_next } = pagination;

        let html = '';

        // 上一页
        html += `
            <li class="page-item ${!has_prev ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${current_page - 1})" ${!has_prev ? 'tabindex="-1"' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, current_page - 2);
        const endPage = Math.min(total_pages, current_page + 2);

        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${total_pages})">${total_pages}</a></li>`;
        }

        // 下一页
        html += `
            <li class="page-item ${!has_next ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${current_page + 1})" ${!has_next ? 'tabindex="-1"' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;

        paginationEl.innerHTML = html;
    }

    function getAsaBadgeColor(grade) {
        const colors = {
            'I': 'success',
            'II': 'info',
            'III': 'warning',
            'IV': 'danger'
        };
        return colors[grade] || 'secondary';
    }

    function updateSortIcons() {
        document.querySelectorAll('.sortable').forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
            if (header.dataset.sort === sortField) {
                header.classList.add(`sort-${sortOrder}`);
            }
        });
    }

    function updateBatchButtons() {
        const hasSelection = selectedRecords.size > 0;
        document.getElementById('batchEditBtn').disabled = !hasSelection;
        document.getElementById('batchDeleteBtn').disabled = !hasSelection;
    }

    function updateSelectAllState() {
        const checkboxes = document.querySelectorAll('.record-checkbox');
        const checkedCount = document.querySelectorAll('.record-checkbox:checked').length;
        const selectAllCheckbox = document.getElementById('selectAll');
        const selectAllHeaderCheckbox = document.getElementById('selectAllHeader');

        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
            selectAllHeaderCheckbox.indeterminate = false;
            selectAllHeaderCheckbox.checked = false;
        } else if (checkedCount === checkboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
            selectAllHeaderCheckbox.indeterminate = false;
            selectAllHeaderCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllHeaderCheckbox.indeterminate = true;
        }
    }

    function toggleSelectAll() {
        const checkboxes = document.querySelectorAll('.record-checkbox');
        const selectAll = this.checked;

        checkboxes.forEach(checkbox => {
            const recordId = parseInt(checkbox.value);
            const row = checkbox.closest('tr');

            checkbox.checked = selectAll;

            if (selectAll) {
                selectedRecords.add(recordId);
                row.classList.add('selected-row');
            } else {
                selectedRecords.delete(recordId);
                row.classList.remove('selected-row');
            }
        });

        updateBatchButtons();

        // 同步两个全选复选框的状态
        document.getElementById('selectAll').checked = selectAll;
        document.getElementById('selectAllHeader').checked = selectAll;
    }

    function performSearch() {
        currentPage = 1;
        loadRecords();
    }

    function toggleFilterPanel() {
        const panel = document.getElementById('filterPanel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    function applyFilters() {
        currentPage = 1;
        loadRecords();
    }

    function resetFilters() {
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';
        document.getElementById('anesthesiaMethod').value = '';
        document.getElementById('department').value = '';
        document.getElementById('asaGrade').value = '';
        document.getElementById('searchInput').value = '';
        currentPage = 1;
        loadRecords();
    }

    // 全局函数
    window.changePage = function(page) {
        if (page >= 1) {
            currentPage = page;
            loadRecords();
        }
    };

    window.viewRecord = function(id) {
        window.location.href = `/data-management/surgery-records/${id}`;
    };

    window.editRecord = function(id) {
        window.location.href = `/data-management/surgery-records/${id}/edit`;
    };

    window.deleteRecord = function(id) {
        if (confirm('确定要删除这条记录吗？')) {
            console.log('开始删除记录，ID:', id);

            // 显示删除中状态
            showAlert('正在删除记录...', 'info');

            fetch(`/api/surgery-records/${id}`, {
                method: 'DELETE',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            })
            .then(response => {
                console.log('删除响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('删除响应数据:', data);

                if (data.success) {
                    showAlert('删除成功', 'success');

                    // 清理前端缓存
                    if (window.PageOptimizer) {
                        window.PageOptimizer.clearCache();
                        console.log('已清理前端缓存');
                    }

                    // 立即刷新数据，不延迟
                    console.log('开始重新加载数据...');

                    // 方法1: 重新加载数据
                    loadRecords();

                    // 方法2: 如果上面不工作，强制刷新页面
                    setTimeout(() => {
                        console.log('🔍 检查数据是否已更新...');
                        // 检查记录是否还在页面上
                        const recordRow = document.querySelector(`tr[data-id="${id}"]`);
                        if (recordRow) {
                            console.log('⚠️ 记录仍在页面上，强制刷新页面');
                            showAlert('正在刷新页面以确保数据同步...', 'info');
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            console.log('✅ 记录已从页面移除，更新成功');
                        }
                    }, 2000); // 增加到2秒，给数据加载更多时间

                    // 同时刷新页面统计信息
                    setTimeout(() => {
                        // 刷新总数显示
                        const totalCountElement = document.getElementById('totalCount');
                        if (totalCountElement) {
                            const currentCount = parseInt(totalCountElement.textContent) || 0;
                            totalCountElement.textContent = Math.max(0, currentCount - 1);
                        }
                    }, 100);

                } else {
                    console.error('删除失败:', data.error);
                    showAlert('删除失败: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('删除请求异常:', error);
                showAlert('删除失败: ' + error.message, 'danger');
            });
        }
    };

    function batchEdit() {
        if (selectedRecords.size === 0) return;

        const ids = Array.from(selectedRecords).join(',');
        window.location.href = `/data-management/surgery-records/batch-edit?ids=${ids}`;
    }

    function batchDelete() {
        if (selectedRecords.size === 0) return;

        const deleteCount = selectedRecords.size;
        if (confirm(`确定要删除选中的 ${deleteCount} 条记录吗？`)) {
            console.log('开始批量删除记录，数量:', deleteCount);

            // 显示删除中状态
            showAlert(`正在删除 ${deleteCount} 条记录...`, 'info');

            fetch('/api/surgery-records/batch-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                },
                body: JSON.stringify({
                    ids: Array.from(selectedRecords)
                })
            })
            .then(response => {
                console.log('批量删除响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('批量删除响应数据:', data);

                if (data.success) {
                    showAlert(`成功删除 ${data.deleted_count} 条记录`, 'success');

                    // 清空选中记录
                    selectedRecords.clear();

                    // 更新批量操作按钮状态
                    updateBatchButtons();

                    // 清理前端缓存
                    if (window.PageOptimizer) {
                        window.PageOptimizer.clearCache();
                        console.log('已清理前端缓存');
                    }

                    // 立即刷新数据
                    console.log('开始重新加载数据...');
                    loadRecords();

                    // 更新总数显示
                    setTimeout(() => {
                        const totalCountElement = document.getElementById('totalCount');
                        if (totalCountElement) {
                            const currentCount = parseInt(totalCountElement.textContent) || 0;
                            totalCountElement.textContent = Math.max(0, currentCount - data.deleted_count);
                        }
                    }, 100);

                } else {
                    console.error('批量删除失败:', data.error);
                    showAlert('批量删除失败: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('批量删除请求异常:', error);
                showAlert('批量删除失败: ' + error.message, 'danger');
            });
        }
    }

    function exportData() {
        const params = new URLSearchParams({
            search: document.getElementById('searchInput').value || '',
            start_date: document.getElementById('startDate').value || '',
            end_date: document.getElementById('endDate').value || '',
            anesthesia_method: document.getElementById('anesthesiaMethod').value || '',
            department: document.getElementById('department').value || '',
            asa_grade: document.getElementById('asaGrade').value || ''
        });

        window.open(`/api/surgery-records/export?${params}`, '_blank');
    }

    function addRecord() {
        window.location.href = '/data-management/surgery-records/add';
    }
});
</script>
{% endblock %}
