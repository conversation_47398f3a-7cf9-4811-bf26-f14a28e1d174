/**
 * 页面加载优化器
 */

class PageOptimizer {
    constructor() {
        this.isOptimized = false;
        this.loadingIndicator = null;
        this.navigationCache = new Map();
    }

    /**
     * 初始化页面优化
     */
    init() {
        if (this.isOptimized) return;

        this.setupLoadingIndicator();
        this.setupNavigationOptimization();
        this.setupFormOptimization();
        this.setupImageOptimization();
        this.isOptimized = true;

        console.log('✅ 页面优化器已初始化');
    }

    /**
     * 设置加载指示器
     */
    setupLoadingIndicator() {
        // 创建全局加载指示器
        this.loadingIndicator = document.createElement('div');
        this.loadingIndicator.id = 'global-loading';
        this.loadingIndicator.innerHTML = `
            <div class="loading-overlay">
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="loading-text">加载中...</div>
                </div>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #global-loading {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                z-index: 9999;
                display: none;
                align-items: center;
                justify-content: center;
            }
            
            #global-loading.show {
                display: flex;
            }
            
            .loading-spinner {
                text-align: center;
            }
            
            .loading-text {
                margin-top: 10px;
                color: #6c757d;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(this.loadingIndicator);
    }

    /**
     * 显示加载指示器
     */
    showLoading(text = '加载中...') {
        if (this.loadingIndicator) {
            this.loadingIndicator.querySelector('.loading-text').textContent = text;
            this.loadingIndicator.classList.add('show');
        }
    }

    /**
     * 隐藏加载指示器
     */
    hideLoading() {
        if (this.loadingIndicator) {
            this.loadingIndicator.classList.remove('show');
        }
    }

    /**
     * 设置导航优化
     */
    setupNavigationOptimization() {
        // 预加载导航链接
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('a[href]');
            if (link && link.hostname === window.location.hostname) {
                this.preloadPage(link.href);
            }
        });

        // 优化页面切换
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && link.hostname === window.location.hostname && !link.target) {
                e.preventDefault();
                this.navigateToPage(link.href);
            }
        });
    }

    /**
     * 预加载页面
     */
    preloadPage(url) {
        if (this.navigationCache.has(url)) return;

        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);

        this.navigationCache.set(url, true);
    }

    /**
     * 优化页面导航
     */
    async navigateToPage(url) {
        this.showLoading('页面跳转中...');
        
        try {
            // 使用fetch预加载页面内容
            const response = await fetch(url);
            if (response.ok) {
                window.location.href = url;
            } else {
                throw new Error('页面加载失败');
            }
        } catch (error) {
            console.error('页面导航失败:', error);
            window.location.href = url; // 降级到普通导航
        }
    }

    /**
     * 设置表单优化
     */
    setupFormOptimization() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM') {
                this.showLoading('提交中...');
                
                // 防止重复提交
                const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    setTimeout(() => {
                        submitBtn.disabled = false;
                    }, 3000);
                }
            }
        });
    }

    /**
     * 设置图片优化
     */
    setupImageOptimization() {
        // 懒加载图片
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('loaded');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px'
            });

            // 观察所有懒加载图片
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });

            // 监听动态添加的图片
            const mutationObserver = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            const lazyImages = node.querySelectorAll ? node.querySelectorAll('img[data-src]') : [];
                            lazyImages.forEach(img => imageObserver.observe(img));
                        }
                    });
                });
            });

            mutationObserver.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    /**
     * 优化AJAX请求
     */
    optimizeAjaxRequests() {
        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            this.showLoading('数据加载中...');
            
            try {
                const response = await originalFetch(...args);
                this.hideLoading();
                return response;
            } catch (error) {
                this.hideLoading();
                throw error;
            }
        };

        // 拦截XMLHttpRequest
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalSend = xhr.send;
            
            xhr.send = function(...args) {
                window.PageOptimizer.showLoading('数据加载中...');
                
                xhr.addEventListener('loadend', () => {
                    window.PageOptimizer.hideLoading();
                });
                
                return originalSend.apply(this, args);
            };
            
            return xhr;
        };
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.navigationCache.clear();
        console.log('✅ 导航缓存已清理');
    }
}

// 全局页面优化器实例
window.PageOptimizer = new PageOptimizer();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.PageOptimizer.init();
    window.PageOptimizer.optimizeAjaxRequests();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    window.PageOptimizer.clearCache();
});
