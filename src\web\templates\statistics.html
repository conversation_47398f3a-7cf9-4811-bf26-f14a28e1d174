{% extends "base.html" %}

{% block title %}统计分析 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}统计分析{% endblock %}

{% block content %}
<!-- 时间范围选择 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            统计分析
                        </h5>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="analysisStartDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="analysisStartDate">
                            </div>
                            <div class="col-md-3">
                                <label for="analysisEndDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="analysisEndDate">
                            </div>
                            <div class="col-md-3">
                                <label for="analysisType" class="form-label">分析维度</label>
                                <select class="form-select" id="analysisType">
                                    <option value="monthly">按月统计</option>
                                    <option value="weekly">按周统计</option>
                                    <option value="daily">按日统计</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button class="btn btn-primary w-100" onclick="refreshAnalysis()">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    更新分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 关键指标卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card primary position-relative">
            <i class="fas fa-procedures"></i>
            <h3 id="totalSurgeries">-</h3>
            <p>总手术量</p>
            <small class="text-light" id="surgeryGrowth">较上期 -</small>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success position-relative">
            <i class="fas fa-user-md"></i>
            <h3 id="avgDuration">-</h3>
            <p>平均手术时长</p>
            <small class="text-light" id="durationTrend">较上期 -</small>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card warning position-relative">
            <i class="fas fa-shield-alt"></i>
            <h3 id="safetyRate">-</h3>
            <p>安全率</p>
            <small class="text-light" id="safetyTrend">较上期 -</small>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info position-relative">
            <i class="fas fa-clock"></i>
            <h3 id="efficiency">-</h3>
            <p>效率指数</p>
            <small class="text-light" id="efficiencyTrend">较上期 -</small>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 趋势分析 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    手术量趋势分析
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="trendType" id="trendVolume" checked>
                    <label class="btn btn-outline-primary" for="trendVolume">手术量</label>
                    
                    <input type="radio" class="btn-check" name="trendType" id="trendDuration">
                    <label class="btn btn-outline-primary" for="trendDuration">时长</label>
                    
                    <input type="radio" class="btn-check" name="trendType" id="trendSafety">
                    <label class="btn btn-outline-primary" for="trendSafety">安全率</label>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 麻醉方式分布 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    麻醉方式分布
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="anesthesiaDistChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细分析 -->
<div class="row mb-4">
    <!-- 手术类型分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    手术类型分析
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="surgeryTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 医生工作量分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-md me-2"></i>
                    医生工作量TOP10
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="doctorWorkloadChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 时间分布热力图 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    手术时间分布热力图
                </h5>
            </div>
            <div class="card-body">
                <div id="heatmapContainer" style="height: 300px;">
                    <!-- 热力图将在这里生成 -->
                    <div class="text-center py-5">
                        <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">时间分布热力图</h6>
                        <p class="text-muted">显示不同时间段的手术分布情况</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计报表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    统计报表
                </h5>
                <button class="btn btn-success btn-sm" onclick="exportReport()">
                    <i class="fas fa-file-excel me-1"></i>
                    导出报表
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>统计项目</th>
                                <th>数值</th>
                                <th>占比</th>
                                <th>同比</th>
                                <th>环比</th>
                            </tr>
                        </thead>
                        <tbody id="statisticsTableBody">
                            <!-- 统计数据将在这里动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 使用立即执行函数避免全局变量冲突
(function() {
    'use strict';

    // 检查是否已经初始化过
    if (window.statisticsInitialized) {
        return;
    }
    window.statisticsInitialized = true;

let charts = {};

document.addEventListener('DOMContentLoaded', function() {
    // 注册Chart.js插件
    Chart.register(ChartDataLabels);
    
    // 设置默认日期范围（最近3个月）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 3);
    
    document.getElementById('analysisEndDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('analysisStartDate').value = startDate.toISOString().split('T')[0];
    
    // 绑定趋势类型切换事件
    document.querySelectorAll('input[name="trendType"]').forEach(radio => {
        radio.addEventListener('change', updateTrendChart);
    });
    
    // 加载统计数据
    loadStatisticsData();
});

function refreshAnalysis() {
    loadStatisticsData();
}

function loadStatisticsData() {
    // 显示加载状态
    showLoadingState();

    // 获取统计数据
    fetch('/api/statistics/comprehensive')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateKeyMetrics(data.metrics);
            updateCharts(data.charts);
            updateStatisticsTable(data.table);
        } else {
            console.error('API返回错误:', data.error);
            // 如果API失败，使用模拟数据
            const mockData = generateMockStatistics();
            updateKeyMetrics(mockData.metrics);
            updateCharts(mockData.charts);
            updateStatisticsTable(mockData.table);
            showAlert('使用模拟数据展示', 'info');
        }
    })
    .catch(error => {
        console.error('统计数据加载失败:', error);
        // 网络错误时使用模拟数据
        const mockData = generateMockStatistics();
        updateKeyMetrics(mockData.metrics);
        updateCharts(mockData.charts);
        updateStatisticsTable(mockData.table);
        showAlert('网络连接失败，使用模拟数据', 'warning');
    })
    .finally(() => {
        hideLoadingState();
    });
}

function generateMockStatistics() {
    return {
        metrics: {
            totalSurgeries: 1250,
            surgeryGrowth: '+12.5%',
            avgDuration: '125分钟',
            durationTrend: '-3.2%',
            safetyRate: '99.2%',
            safetyTrend: '+0.5%',
            efficiency: '85.6',
            efficiencyTrend: '+7.8%'
        },
        charts: {
            trend: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                volume: [320, 285, 390, 410, 380, 450],
                duration: [128, 132, 125, 120, 118, 125],
                safety: [98.5, 99.1, 99.2, 98.8, 99.5, 99.2]
            },
            anesthesia: {
                labels: ['全身麻醉', '椎管内麻醉', '神经阻滞麻醉', '静脉麻醉', '局部麻醉'],
                data: [450, 280, 180, 120, 80]
            },
            surgeryType: {
                labels: ['择期手术', '急诊手术', '日间手术', '门诊手术'],
                data: [680, 320, 180, 70]
            },
            doctorWorkload: {
                labels: ['张医生', '李医生', '王医生', '赵医生', '刘医生', '陈医生', '杨医生', '黄医生', '周医生', '吴医生'],
                data: [85, 78, 72, 68, 65, 62, 58, 55, 52, 48]
            }
        },
        table: [
            { item: '全身麻醉', value: 450, percentage: '36.0%', yoy: '****%', mom: '****%' },
            { item: '椎管内麻醉', value: 280, percentage: '22.4%', yoy: '+12.3%', mom: '-1.5%' },
            { item: '神经阻滞麻醉', value: 180, percentage: '14.4%', yoy: '+15.2%', mom: '****%' },
            { item: '择期手术', value: 680, percentage: '54.4%', yoy: '****%', mom: '****%' },
            { item: '急诊手术', value: 320, percentage: '25.6%', yoy: '+18.5%', mom: '****%' }
        ]
    };
}

function updateKeyMetrics(metrics) {
    document.getElementById('totalSurgeries').textContent = formatNumber(metrics.totalSurgeries);
    document.getElementById('surgeryGrowth').textContent = metrics.surgeryGrowth;
    document.getElementById('avgDuration').textContent = metrics.avgDuration;
    document.getElementById('durationTrend').textContent = metrics.durationTrend;
    document.getElementById('safetyRate').textContent = metrics.safetyRate;
    document.getElementById('safetyTrend').textContent = metrics.safetyTrend;
    document.getElementById('efficiency').textContent = metrics.efficiency;
    document.getElementById('efficiencyTrend').textContent = metrics.efficiencyTrend;
}

function updateCharts(chartData) {
    // 销毁现有图表
    Object.values(charts).forEach(chart => {
        if (chart) chart.destroy();
    });
    
    // 趋势图
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    charts.trend = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: chartData.trend.labels,
            datasets: [{
                label: '手术量',
                data: chartData.trend.volume,
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: { beginAtZero: true }
            },
            plugins: {
                datalabels: {
                    display: true,
                    color: 'black',
                    font: { weight: 'bold', size: 10 },
                    anchor: 'end',
                    align: 'top'
                }
            }
        }
    });
    
    // 麻醉方式分布
    const anesthesiaCtx = document.getElementById('anesthesiaDistChart').getContext('2d');
    charts.anesthesia = new Chart(anesthesiaCtx, {
        type: 'doughnut',
        data: {
            labels: chartData.anesthesia.labels,
            datasets: [{
                data: chartData.anesthesia.data,
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'bottom' },
                datalabels: {
                    display: true,
                    color: 'white',
                    font: { weight: 'bold', size: 12 },
                    formatter: function(value, context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        return `${percentage}%`;
                    }
                }
            }
        }
    });
    
    // 手术类型分析
    const surgeryTypeCtx = document.getElementById('surgeryTypeChart').getContext('2d');
    charts.surgeryType = new Chart(surgeryTypeCtx, {
        type: 'bar',
        data: {
            labels: chartData.surgeryType.labels,
            datasets: [{
                label: '手术量',
                data: chartData.surgeryType.data,
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: { beginAtZero: true }
            },
            plugins: {
                datalabels: {
                    display: true,
                    color: 'black',
                    font: { weight: 'bold', size: 12 },
                    anchor: 'end',
                    align: 'top'
                }
            }
        }
    });
    
    // 医生工作量
    const doctorCtx = document.getElementById('doctorWorkloadChart').getContext('2d');
    charts.doctor = new Chart(doctorCtx, {
        type: 'bar',
        data: {
            labels: chartData.doctorWorkload.labels,
            datasets: [{
                label: '手术量',
                data: chartData.doctorWorkload.data,
                backgroundColor: '#36A2EB'
            }]
        },
        options: {
            indexAxis: 'y', // 使条形图变为水平
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: { beginAtZero: true }
            },
            plugins: {
                datalabels: {
                    display: true,
                    color: 'black',
                    font: { weight: 'bold', size: 10 },
                    anchor: 'end',
                    align: 'right'
                }
            }
        }
    });
}

function updateTrendChart() {
    const selectedType = document.querySelector('input[name="trendType"]:checked').id;
    
    if (!charts.trend) return;
    
    let data, label, color;
    
    switch (selectedType) {
        case 'trendVolume':
            data = [320, 285, 390, 410, 380, 450];
            label = '手术量';
            color = '#36A2EB';
            break;
        case 'trendDuration':
            data = [128, 132, 125, 120, 118, 125];
            label = '平均时长(分钟)';
            color = '#FF6384';
            break;
        case 'trendSafety':
            data = [98.5, 99.1, 99.2, 98.8, 99.5, 99.2];
            label = '安全率(%)';
            color = '#4BC0C0';
            break;
    }
    
    charts.trend.data.datasets[0].data = data;
    charts.trend.data.datasets[0].label = label;
    charts.trend.data.datasets[0].borderColor = color;
    charts.trend.data.datasets[0].backgroundColor = color + '20';
    charts.trend.update();
}

function updateStatisticsTable(tableData) {
    const tbody = document.getElementById('statisticsTableBody');
    tbody.innerHTML = '';
    
    tableData.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td><strong>${row.item}</strong></td>
            <td>${formatNumber(row.value)}</td>
            <td><span class="badge bg-primary">${row.percentage}</span></td>
            <td><span class="badge ${row.yoy.startsWith('+') ? 'bg-success' : 'bg-danger'}">${row.yoy}</span></td>
            <td><span class="badge ${row.mom.startsWith('+') ? 'bg-success' : 'bg-danger'}">${row.mom}</span></td>
        `;
        tbody.appendChild(tr);
    });
}

function showLoadingState() {
    // 显示所有指标的加载状态
    document.getElementById('totalSurgeries').textContent = '-';
    document.getElementById('avgDuration').textContent = '-';
    document.getElementById('safetyRate').textContent = '-';
    document.getElementById('efficiency').textContent = '-';

    // 显示趋势文本
    document.getElementById('surgeryGrowth').textContent = '加载中...';
    document.getElementById('durationTrend').textContent = '加载中...';
    document.getElementById('safetyTrend').textContent = '加载中...';
    document.getElementById('efficiencyTrend').textContent = '加载中...';
}

function hideLoadingState() {
    // 加载完成后的处理
    console.log('数据加载完成');
}

function formatNumber(num) {
    if (typeof num === 'number') {
        return num.toLocaleString();
    }
    return num;
}

function showAlert(message, type) {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

function exportReport() {
    showAlert('正在生成统计报表...', 'info');

    // 模拟导出过程
    setTimeout(() => {
        showAlert('统计报表导出完成！', 'success');

        // 这里可以添加实际的导出逻辑
        // window.open('/api/statistics/export', '_blank');
    }, 2000);
}

})(); // 结束立即执行函数
</script>
{% endblock %}
