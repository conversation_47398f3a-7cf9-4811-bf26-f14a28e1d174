#!/usr/bin/env python3
"""
数据处理管道测试脚本
测试数据清洗、脱敏和处理管道功能
"""
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

import pandas as pd
from services.data_cleaner import DataCleaner, AnesthesiaMethodCleaner
from services.data_anonymizer import DataAnonymizer
from services.data_pipeline import DataProcessingPipeline, create_default_config


def create_test_data():
    """创建测试数据"""
    test_data = {
        '患者姓名': ['张三', '李四', '王五', '赵六', '钱七'],
        '年龄': ['45岁', '32', '67', '28岁', '55'],
        '性别': ['男', 'M', '女', 'F', '男'],
        '联系电话': ['13812345678', '139-8765-4321', '15012345678', '186 1234 5678', '17712345678'],
        '身份证号': ['110101199001011234', '220102198505052345', '330103197012123456', '440104199203034567', '550105198808088901'],
        '麻醉方法': [
            '全身麻醉（气管插管）',
            '硬膜外麻醉+局部麻醉',
            '神经阻滞麻醉（超声引导）',
            '静脉麻醉',
            '椎管内麻醉（腰硬联合）'
        ],
        '手术日期': ['2024-01-15', '2024/01/16', '20240117', '2024-1-18', '2024.01.19'],
        '手术类型': ['择期手术', '急诊手术', '日间手术', '择期手术', '急诊手术'],
        '科室': ['骨科', '普外科', '妇产科', '泌尿外科', '神经外科'],
        '麻醉医生': ['张医生', '李医生', '王医生', '赵医生', '钱医生'],
        '手术医生': ['陈医生', '刘医生', '杨医生', '周医生', '吴医生']
    }
    
    return pd.DataFrame(test_data)


def test_anesthesia_cleaner():
    """测试麻醉方法清洗器"""
    print("=" * 50)
    print("测试麻醉方法清洗器")
    print("=" * 50)
    
    cleaner = AnesthesiaMethodCleaner()
    
    test_methods = [
        '全身麻醉（气管插管）',
        '硬膜外麻醉+局部麻醉',
        '神经阻滞麻醉（超声引导）',
        '静脉麻醉',
        '椎管内麻醉（腰硬联合）',
        '全身吸入麻醉',
        '静吸复合麻醉',
        '蛛网膜下腔阻滞',
        '臂丛神经阻滞'
    ]
    
    for method in test_methods:
        result = cleaner.clean_anesthesia_method(method)
        print(f"原始: {method}")
        print(f"清洗后: {result['cleaned']}")
        print(f"主要方法: {result['primary_anesthesia']}")
        print(f"复合方法: {result['compound_anesthesia']}")
        print("-" * 30)


def test_data_cleaner():
    """测试数据清洗器"""
    print("=" * 50)
    print("测试数据清洗器")
    print("=" * 50)
    
    # 创建测试数据
    df = create_test_data()
    print("原始数据:")
    print(df)
    print()
    
    # 清洗数据
    cleaner = DataCleaner()
    cleaned_df = cleaner.clean_dataframe(df)
    
    print("清洗后数据:")
    print(cleaned_df)
    print()
    
    # 显示清洗摘要
    summary = cleaner.get_cleaning_summary(df, cleaned_df)
    print("清洗摘要:")
    for key, value in summary.items():
        print(f"{key}: {value}")


def test_data_anonymizer():
    """测试数据脱敏器"""
    print("=" * 50)
    print("测试数据脱敏器")
    print("=" * 50)
    
    # 创建测试数据
    df = create_test_data()
    print("原始数据:")
    print(df[['患者姓名', '联系电话', '身份证号']])
    print()
    
    # 脱敏数据
    anonymizer = DataAnonymizer()
    anonymized_df = anonymizer.anonymize_dataframe(df)
    
    print("脱敏后数据:")
    anonymized_columns = [col for col in anonymized_df.columns if col.endswith('_anonymized')]
    print(anonymized_df[['患者姓名', '联系电话', '身份证号'] + anonymized_columns])
    print()
    
    # 显示脱敏摘要
    summary = anonymizer.get_anonymization_summary(df, anonymized_df)
    print("脱敏摘要:")
    for key, value in summary.items():
        print(f"{key}: {value}")


def test_processing_pipeline():
    """测试处理管道"""
    print("=" * 50)
    print("测试处理管道")
    print("=" * 50)
    
    # 创建测试文件
    test_file = "test_data.xlsx"
    df = create_test_data()
    df.to_excel(test_file, index=False)
    print(f"创建测试文件: {test_file}")
    
    # 创建处理管道
    pipeline = DataProcessingPipeline()
    
    # 验证文件
    print("\n1. 验证文件...")
    validation_result = pipeline.validate_file(test_file)
    print(f"验证结果: {validation_result}")
    
    # 处理文件
    print("\n2. 处理文件...")
    config = create_default_config()
    
    result = pipeline.process_file(
        file_path=test_file,
        output_dir="test_output",
        clean_data=True,
        anonymize_data=True,
        field_mapping=config['field_mapping'],
        anonymize_config=config['anonymize_config'],
        save_intermediate=True
    )
    
    print("处理结果:")
    for key, value in result.items():
        if key not in ['processing_steps']:  # 跳过详细步骤
            print(f"{key}: {value}")
    
    print("\n处理步骤:")
    for step in result.get('processing_steps', []):
        print(f"- {step['step']}: {step['status']}")
    
    # 清理测试文件
    try:
        os.remove(test_file)
        print(f"\n清理测试文件: {test_file}")
    except:
        pass


def test_different_anonymization_methods():
    """测试不同的脱敏方法"""
    print("=" * 50)
    print("测试不同脱敏方法")
    print("=" * 50)
    
    from services.data_anonymizer import NameAnonymizer, PhoneAnonymizer, IdCardAnonymizer
    
    name_anonymizer = NameAnonymizer()
    phone_anonymizer = PhoneAnonymizer()
    id_anonymizer = IdCardAnonymizer()
    
    test_name = "张三丰"
    test_phone = "13812345678"
    test_id = "110101199001011234"
    
    print(f"原始姓名: {test_name}")
    print(f"掩码脱敏: {name_anonymizer.anonymize_name(test_name, 'mask')}")
    print(f"替换脱敏: {name_anonymizer.anonymize_name(test_name, 'replace')}")
    print(f"哈希脱敏: {name_anonymizer.anonymize_name(test_name, 'hash')}")
    print()
    
    print(f"原始电话: {test_phone}")
    print(f"掩码脱敏: {phone_anonymizer.anonymize_phone(test_phone, 'mask')}")
    print(f"替换脱敏: {phone_anonymizer.anonymize_phone(test_phone, 'replace')}")
    print(f"哈希脱敏: {phone_anonymizer.anonymize_phone(test_phone, 'hash')}")
    print()
    
    print(f"原始身份证: {test_id}")
    print(f"掩码脱敏: {id_anonymizer.anonymize_id_card(test_id, 'mask')}")
    print(f"替换脱敏: {id_anonymizer.anonymize_id_card(test_id, 'replace')}")
    print(f"哈希脱敏: {id_anonymizer.anonymize_id_card(test_id, 'hash')}")


def main():
    """主函数"""
    print("数据处理管道测试")
    print("=" * 80)
    
    try:
        # 测试各个组件
        test_anesthesia_cleaner()
        print("\n")
        
        test_data_cleaner()
        print("\n")
        
        test_data_anonymizer()
        print("\n")
        
        test_different_anonymization_methods()
        print("\n")
        
        test_processing_pipeline()
        
        print("\n" + "=" * 80)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
