# 数据处理管道使用指南

## 概述

本系统实现了一个完整的数据处理管道，整合了数据清洗、脱敏和验证功能，特别针对麻醉质控数据的特点进行了优化。

## 功能特性

### 1. 数据清洗 (Data Cleaning)
- **麻醉方法标准化**: 基于 `example/anesthesia_method_cleaner.py` 的逻辑
- **字段格式清理**: 姓名、电话、身份证、日期等
- **数据验证**: 年龄、性别等字段的合理性检查

### 2. 数据脱敏 (Data Anonymization)
- **姓名脱敏**: 掩码、替换、哈希三种方式
- **电话脱敏**: 保持格式的安全脱敏
- **身份证脱敏**: 保护隐私的同时保持数据可用性

### 3. 模块化设计
- **独立组件**: 每个功能模块可单独使用
- **配置驱动**: 通过配置文件控制处理行为
- **管道集成**: 统一的处理流程

## 模块结构

```
src/services/
├── data_cleaner.py          # 数据清洗模块
├── data_anonymizer.py       # 数据脱敏模块
├── data_pipeline.py         # 数据处理管道
└── data_service.py          # 数据服务（已更新）
```

## 使用方法

### 1. 基础使用

#### 单独使用麻醉方法清洗器
```python
from services.data_cleaner import AnesthesiaMethodCleaner

cleaner = AnesthesiaMethodCleaner()
result = cleaner.clean_anesthesia_method('全身麻醉（气管插管）')

print(result)
# {
#     'original': '全身麻醉（气管插管）',
#     'cleaned': '全身麻醉',
#     'primary_anesthesia': '全身麻醉',
#     'compound_anesthesia': '',
#     'primary_raw': '全身麻醉',
#     'compound_raw': ''
# }
```

#### 数据脱敏
```python
from services.data_anonymizer import DataAnonymizer

anonymizer = DataAnonymizer()
anonymized_df = anonymizer.anonymize_dataframe(df, {
    '患者姓名': {'type': 'name', 'method': 'mask'},
    '联系电话': {'type': 'phone', 'method': 'mask'},
    '身份证号': {'type': 'id_card', 'method': 'mask'}
})
```

### 2. 完整处理管道

#### 处理单个文件
```python
from services.data_pipeline import DataProcessingPipeline, create_default_config

pipeline = DataProcessingPipeline()
config = create_default_config()

result = pipeline.process_file(
    file_path="data.xlsx",
    output_dir="output",
    clean_data=True,
    anonymize_data=True,
    field_mapping=config['field_mapping'],
    anonymize_config=config['anonymize_config']
)
```

#### 批量处理
```python
file_paths = ["file1.xlsx", "file2.xlsx", "file3.xlsx"]
results = pipeline.process_batch(file_paths, output_dir="batch_output")
```

### 3. Web API 使用

#### 处理文件
```bash
curl -X POST http://localhost:5000/api/process-file \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "uploads/data.xlsx",
    "clean_data": true,
    "anonymize_data": true,
    "import_to_db": true
  }'
```

#### 验证文件
```bash
curl -X POST http://localhost:5000/api/validate-file \
  -H "Content-Type: application/json" \
  -d '{"file_path": "uploads/data.xlsx"}'
```

#### 获取配置
```bash
curl http://localhost:5000/api/processing-config
```

## 配置说明

### 字段映射配置
```python
field_mapping = {
    '麻醉方法': 'anesthesia_method',
    '患者姓名': 'patient_name',
    '年龄': 'age',
    '性别': 'gender',
    '联系电话': 'phone',
    '身份证号': 'id_card',
    '手术日期': 'surgery_date'
}
```

### 脱敏配置
```python
anonymize_config = {
    '患者姓名': {'type': 'name', 'method': 'mask'},
    '联系电话': {'type': 'phone', 'method': 'mask'},
    '身份证号': {'type': 'id_card', 'method': 'mask'}
}
```

### 脱敏方法说明
- **mask**: 掩码方式（如：张*丰）
- **replace**: 替换方式（生成新的假数据）
- **hash**: 哈希方式（生成唯一标识符）

## 麻醉方法清洗规则

基于原始 `anesthesia_method_cleaner.py` 的逻辑，支持以下标准化：

| 原始术语 | 标准化结果 |
|---------|-----------|
| 全身、吸入、静吸 | 全身麻醉 |
| 硬、骶管、椎管、蛛网膜、腰麻、硬膜外 | 椎管内麻醉 |
| 神经、臂丛、坐骨、股神经 | 神经阻滞麻醉 |
| 静脉 | 静脉麻醉 |
| 局部 | 局部麻醉 |

## 输出文件说明

处理完成后会生成以下文件：
- `*_cleaned.xlsx`: 清洗后的数据
- `*_anonymized.xlsx`: 脱敏后的数据
- `*_processed.xlsx`: 最终处理结果
- `*_report.json`: 处理报告

## 测试

运行测试脚本：
```bash
python test_data_pipeline.py
```

测试内容包括：
- 麻醉方法清洗器测试
- 数据清洗器测试
- 数据脱敏器测试
- 完整处理管道测试

## 性能优化

1. **批量处理**: 支持多文件批量处理
2. **内存优化**: 大文件分块处理
3. **缓存机制**: 重复数据的缓存处理
4. **并行处理**: 支持多线程处理（可扩展）

## 错误处理

系统提供完善的错误处理机制：
- 文件格式验证
- 数据完整性检查
- 处理过程异常捕获
- 详细的错误日志

## 扩展性

### 添加新的清洗规则
```python
class CustomCleaner:
    def clean_custom_field(self, value):
        # 自定义清洗逻辑
        return cleaned_value
```

### 添加新的脱敏方法
```python
class CustomAnonymizer:
    def anonymize_custom_field(self, value, method):
        # 自定义脱敏逻辑
        return anonymized_value
```

## 注意事项

1. **数据备份**: 处理前请备份原始数据
2. **隐私保护**: 脱敏后的数据仍需妥善保管
3. **配置验证**: 确保配置文件的正确性
4. **文件格式**: 支持 .xlsx, .xls, .csv 格式
5. **编码问题**: CSV文件请使用UTF-8编码

## 常见问题

### Q: 如何自定义麻醉方法映射？
A: 修改 `AnesthesiaMethodCleaner` 类中的 `anesthesia_mapping` 字典。

### Q: 如何添加新的脱敏字段？
A: 在 `anonymize_config` 中添加新的字段配置。

### Q: 处理大文件时内存不足怎么办？
A: 可以调整 `chunk_size` 参数，分块处理大文件。

### Q: 如何恢复脱敏前的数据？
A: 使用 `create_anonymization_mapping` 方法创建映射表，但请注意安全性。

## 更新日志

- **v1.0**: 初始版本，基础清洗和脱敏功能
- **v1.1**: 添加麻醉方法标准化
- **v1.2**: 集成处理管道和Web API
- **v1.3**: 添加批量处理和性能优化
