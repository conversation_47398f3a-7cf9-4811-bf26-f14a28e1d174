{% extends "base.html" %}

{% block title %}患者管理 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item active" aria-current="page">患者管理</li>
        </ol>
    </nav>
{% endblock %}

{% block content %}
<!-- 筛选和搜索 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-injured me-2"></i>
                    患者信息管理
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label for="searchInput" class="form-label">搜索患者</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="姓名、ID或联系方式">
                    </div>
                    <div class="col-md-2">
                        <label for="genderFilter" class="form-label">性别</label>
                        <select class="form-select" id="genderFilter">
                            <option value="">全部</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="ageMinFilter" class="form-label">最小年龄</label>
                        <input type="number" class="form-control" id="ageMinFilter" min="0" max="120">
                    </div>
                    <div class="col-md-2">
                        <label for="ageMaxFilter" class="form-label">最大年龄</label>
                        <input type="number" class="form-control" id="ageMaxFilter" min="0" max="120">
                    </div>
                    <div class="col-md-2">
                        <label for="dataStatusFilter" class="form-label">数据状态</label>
                        <select class="form-select" id="dataStatusFilter">
                            <option value="">全部</option>
                            <option value="normal">正常</option>
                            <option value="masked">已脱敏</option>
                        </select>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button class="btn btn-primary w-100" onclick="searchPatients()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <div class="text-primary">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary mb-1" id="totalPatients">0</h4>
                <small class="text-muted">总患者数</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <div class="text-success">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                </div>
                <h4 class="text-success mb-1" id="newPatients">0</h4>
                <small class="text-muted">本月新增</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <div class="text-info">
                    <i class="fas fa-procedures fa-2x mb-2"></i>
                </div>
                <h4 class="text-info mb-1" id="avgSurgeries">0</h4>
                <small class="text-muted">平均手术次数</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <div class="text-warning">
                    <i class="fas fa-shield-alt fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning mb-1" id="maskedPatients">0</h4>
                <small class="text-muted">已脱敏患者</small>
            </div>
        </div>
    </div>
</div>

<!-- 患者列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    患者列表
                </h6>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="exportPatients()">
                        <i class="fas fa-download me-1"></i>
                        导出
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="addPatient()">
                        <i class="fas fa-plus me-1"></i>
                        新增患者
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 加载状态 -->
                <div id="loadingIndicator" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载患者数据...</div>
                </div>

                <!-- 患者表格 -->
                <div id="patientsTableContainer" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>患者ID</th>
                                    <th>姓名</th>
                                    <th>年龄</th>
                                    <th>性别</th>
                                    <th>联系方式</th>
                                    <th>手术次数</th>
                                    <th>数据状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="patientsTableBody">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="患者列表分页" id="paginationContainer">
                        <!-- 动态生成分页 -->
                    </nav>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无患者数据</h5>
                    <p class="text-muted">您可以通过数据导入功能添加患者信息</p>
                    <a href="/data-import" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        导入数据
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作工具栏 -->
<div id="batchToolbar" class="position-fixed bottom-0 start-50 translate-middle-x bg-primary text-white p-3 rounded-top shadow" style="display: none; z-index: 1050;">
    <div class="d-flex align-items-center gap-3">
        <span>已选择 <span id="selectedCount">0</span> 个患者</span>
        <button class="btn btn-light btn-sm" onclick="batchExport()">
            <i class="fas fa-download me-1"></i>
            批量导出
        </button>
        <button class="btn btn-danger btn-sm" onclick="batchDelete()">
            <i class="fas fa-trash me-1"></i>
            批量删除
        </button>
        <button class="btn btn-outline-light btn-sm" onclick="clearSelection()">
            <i class="fas fa-times me-1"></i>
            取消选择
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 患者管理相关的JavaScript代码
let currentPage = 1;
let pageSize = 25;
let selectedPatients = new Set();

document.addEventListener('DOMContentLoaded', function() {
    loadPatients();
    loadStatistics();
});

function loadPatients() {
    showLoading();
    
    const params = new URLSearchParams({
        page: currentPage,
        page_size: pageSize,
        search: document.getElementById('searchInput').value || '',
        gender: document.getElementById('genderFilter').value || '',
        min_age: document.getElementById('ageMinFilter').value || '',
        max_age: document.getElementById('ageMaxFilter').value || '',
        data_status: document.getElementById('dataStatusFilter').value || ''
    });

    fetch(`/api/patients?${params}`)
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            displayPatients(data.patients);
            updatePagination(data.pagination);
        } else {
            showError('加载患者数据失败: ' + data.error);
        }
    })
    .catch(error => {
        hideLoading();
        showError('网络错误: ' + error.message);
    });
}

function loadStatistics() {
    // 加载患者统计信息
    fetch('/api/patients/statistics')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('totalPatients').textContent = data.total || 0;
            document.getElementById('newPatients').textContent = data.new_this_month || 0;
            document.getElementById('avgSurgeries').textContent = data.avg_surgeries || 0;
            document.getElementById('maskedPatients').textContent = data.masked_count || 0;
        }
    })
    .catch(error => {
        console.error('加载统计信息失败:', error);
    });
}

function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('patientsTableContainer').style.display = 'none';
    document.getElementById('emptyState').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

function displayPatients(patients) {
    const tbody = document.getElementById('patientsTableBody');
    
    if (patients.length === 0) {
        document.getElementById('emptyState').style.display = 'block';
        return;
    }
    
    document.getElementById('patientsTableContainer').style.display = 'block';
    
    tbody.innerHTML = patients.map(patient => `
        <tr>
            <td>
                <input type="checkbox" value="${patient.id}" onchange="togglePatientSelection(${patient.id})">
            </td>
            <td>${patient.patient_id || '-'}</td>
            <td>${patient.masked_name || patient.original_name || '-'}</td>
            <td>${patient.age || '-'}</td>
            <td>${patient.gender || '-'}</td>
            <td>${patient.contact || '-'}</td>
            <td>
                <span class="badge bg-info">${patient.surgery_count || 0}</span>
            </td>
            <td>
                ${patient.masked_name ? 
                    '<span class="badge bg-warning">已脱敏</span>' : 
                    '<span class="badge bg-success">正常</span>'
                }
            </td>
            <td>${new Date(patient.created_at).toLocaleDateString()}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewPatient(${patient.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="editPatient(${patient.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deletePatient(${patient.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function searchPatients() {
    currentPage = 1;
    selectedPatients.clear();
    updateBatchToolbar();
    loadPatients();
}

function viewPatient(patientId) {
    window.location.href = `/patients/${patientId}`;
}

function editPatient(patientId) {
    window.location.href = `/patients/${patientId}/edit`;
}

function addPatient() {
    window.location.href = '/patients/add';
}

function deletePatient(patientId) {
    if (!confirm('确定要删除这个患者吗？删除后无法恢复。')) return;
    
    fetch(`/api/patients/${patientId}`, { method: 'DELETE' })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('患者删除成功');
            loadPatients();
            loadStatistics();
        } else {
            showError('删除失败: ' + data.error);
        }
    })
    .catch(error => {
        showError('网络错误: ' + error.message);
    });
}

function togglePatientSelection(patientId) {
    const checkbox = event.target;
    if (checkbox.checked) {
        selectedPatients.add(patientId);
    } else {
        selectedPatients.delete(patientId);
    }
    updateBatchToolbar();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('#patientsTableBody input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        const patientId = parseInt(checkbox.value);
        if (selectAll.checked) {
            selectedPatients.add(patientId);
        } else {
            selectedPatients.delete(patientId);
        }
    });
    
    updateBatchToolbar();
}

function updateBatchToolbar() {
    const toolbar = document.getElementById('batchToolbar');
    const count = selectedPatients.size;
    
    if (count > 0) {
        document.getElementById('selectedCount').textContent = count;
        toolbar.style.display = 'block';
    } else {
        toolbar.style.display = 'none';
    }
}

function clearSelection() {
    selectedPatients.clear();
    document.querySelectorAll('#patientsTableBody input[type="checkbox"]').forEach(cb => cb.checked = false);
    document.getElementById('selectAll').checked = false;
    updateBatchToolbar();
}

function updatePagination(pagination) {
    // 实现分页逻辑
    const container = document.getElementById('paginationContainer');
    if (pagination.total_pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let html = '<ul class="pagination justify-content-center">';
    
    // 上一页
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.current_page - 1})">上一页</a></li>`;
    }
    
    // 页码
    for (let i = 1; i <= pagination.total_pages; i++) {
        if (i === pagination.current_page) {
            html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
        }
    }
    
    // 下一页
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.current_page + 1})">下一页</a></li>`;
    }
    
    html += '</ul>';
    container.innerHTML = html;
}

function changePage(page) {
    currentPage = page;
    loadPatients();
}

function showSuccess(message) {
    // 实现成功提示
    alert(message);
}

function showError(message) {
    // 实现错误提示
    alert(message);
}

function exportPatients() {
    window.location.href = '/api/patients/export';
}

function batchExport() {
    const ids = Array.from(selectedPatients);
    if (ids.length === 0) return;
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/api/patients/batch-export';
    
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'ids';
    input.value = JSON.stringify(ids);
    
    form.appendChild(input);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

function batchDelete() {
    const ids = Array.from(selectedPatients);
    if (ids.length === 0) return;
    
    if (!confirm(`确定要删除选中的 ${ids.length} 个患者吗？删除后无法恢复。`)) return;
    
    fetch('/api/patients/batch-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(`成功删除 ${data.deleted_count} 个患者`);
            clearSelection();
            loadPatients();
            loadStatistics();
        } else {
            showError('批量删除失败: ' + data.error);
        }
    })
    .catch(error => {
        showError('网络错误: ' + error.message);
    });
}
</script>
{% endblock %}
