/**
 * 高级数据表格组件
 */
class AdvancedDataTable {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            columns: [],
            data: [],
            selectable: false,
            sortable: true,
            searchable: true,
            filterable: true,
            exportable: true,
            pagination: true,
            pageSize: 25,
            apiEndpoint: null,
            onSort: () => {},
            onSelect: () => {},
            onFilter: () => {},
            onRowClick: () => {},
            ...options
        };
        
        this.state = {
            data: [],
            filteredData: [],
            selectedRows: new Set(),
            currentPage: 1,
            sortField: null,
            sortOrder: 'asc',
            filters: {},
            searchTerm: ''
        };
        
        this.init();
    }

    init() {
        this.createStructure();
        this.bindEvents();
        if (this.options.data.length > 0) {
            this.setData(this.options.data);
        }
    }

    createStructure() {
        this.container.innerHTML = `
            <div class="data-table-container">
                ${this.options.searchable ? this.createSearchBar() : ''}
                ${this.options.filterable ? this.createFilterBar() : ''}
                <div class="table-wrapper">
                    <div class="table-loading" style="display: none;">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary"></div>
                            <p class="mt-2">加载中...</p>
                        </div>
                    </div>
                    <table class="table table-hover">
                        <thead class="table-dark">
                            ${this.createTableHeader()}
                        </thead>
                        <tbody class="table-body">
                            ${this.createEmptyState()}
                        </tbody>
                    </table>
                </div>
                ${this.options.pagination ? this.createPagination() : ''}
                ${this.options.exportable ? this.createExportButtons() : ''}
            </div>
        `;
    }

    createSearchBar() {
        return `
            <div class="table-search mb-3">
                <div class="input-group">
                    <input type="text" class="form-control search-input" 
                           placeholder="搜索...">
                    <button class="btn btn-outline-secondary search-btn" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-outline-secondary clear-search-btn" type="button">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    }

    createFilterBar() {
        return `
            <div class="table-filters mb-3" style="display: none;">
                <div class="row">
                    ${this.options.columns
                        .filter(col => col.filterable)
                        .map(col => this.createColumnFilter(col))
                        .join('')}
                </div>
            </div>
        `;
    }

    createColumnFilter(column) {
        const filterId = `filter-${column.field}`;
        return `
            <div class="col-md-3 mb-2">
                <label class="form-label">${column.title}</label>
                ${column.filterType === 'select' 
                    ? this.createSelectFilter(filterId, column.filterOptions || [])
                    : this.createTextFilter(filterId, column.title)
                }
            </div>
        `;
    }

    createSelectFilter(id, options) {
        return `
            <select class="form-select column-filter" data-field="${id}">
                <option value="">全部</option>
                ${options.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('')}
            </select>
        `;
    }

    createTextFilter(id, placeholder) {
        return `
            <input type="text" class="form-control column-filter" 
                   data-field="${id}" placeholder="筛选${placeholder}">
        `;
    }

    createTableHeader() {
        let html = '<tr>';
        
        if (this.options.selectable) {
            html += `
                <th width="40">
                    <input type="checkbox" class="form-check-input select-all-checkbox">
                </th>
            `;
        }
        
        this.options.columns.forEach(col => {
            const sortable = col.sortable !== false && this.options.sortable;
            const sortIcon = this.getSortIcon(col.field);
            
            html += `
                <th class="${sortable ? 'sortable' : ''}" 
                    data-field="${col.field}" 
                    ${col.width ? `width="${col.width}"` : ''}>
                    ${col.title}
                    ${sortable ? sortIcon : ''}
                </th>
            `;
        });
        
        html += '</tr>';
        return html;
    }

    createEmptyState() {
        const colSpan = this.options.columns.length + (this.options.selectable ? 1 : 0);
        return `
            <tr class="empty-state">
                <td colspan="${colSpan}" class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无数据</p>
                </td>
            </tr>
        `;
    }

    createPagination() {
        return `
            <div class="table-pagination mt-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center gap-2">
                            <span>每页显示:</span>
                            <select class="form-select form-select-sm page-size-select" style="width: auto;">
                                <option value="10">10</option>
                                <option value="25" selected>25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>条记录</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <nav aria-label="分页导航">
                            <ul class="pagination pagination-sm justify-content-end mb-0 pagination-list">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        `;
    }

    createExportButtons() {
        return `
            <div class="table-export mt-3">
                <div class="btn-group">
                    <button class="btn btn-outline-secondary btn-sm export-csv-btn">
                        <i class="fas fa-file-csv me-1"></i>导出CSV
                    </button>
                    <button class="btn btn-outline-secondary btn-sm export-excel-btn">
                        <i class="fas fa-file-excel me-1"></i>导出Excel
                    </button>
                    <button class="btn btn-outline-secondary btn-sm export-pdf-btn">
                        <i class="fas fa-file-pdf me-1"></i>导出PDF
                    </button>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 搜索事件
        if (this.options.searchable) {
            const searchInput = this.container.querySelector('.search-input');
            const searchBtn = this.container.querySelector('.search-btn');
            const clearBtn = this.container.querySelector('.clear-search-btn');
            
            const debouncedSearch = this.debounce(() => this.performSearch(), 300);
            
            searchInput?.addEventListener('input', debouncedSearch);
            searchBtn?.addEventListener('click', () => this.performSearch());
            clearBtn?.addEventListener('click', () => this.clearSearch());
        }

        // 排序事件
        this.container.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', () => this.handleSort(th.dataset.field));
        });

        // 选择事件
        if (this.options.selectable) {
            const selectAllCheckbox = this.container.querySelector('.select-all-checkbox');
            selectAllCheckbox?.addEventListener('change', (e) => this.handleSelectAll(e.target.checked));
        }

        // 分页事件
        if (this.options.pagination) {
            const pageSizeSelect = this.container.querySelector('.page-size-select');
            pageSizeSelect?.addEventListener('change', (e) => {
                this.options.pageSize = parseInt(e.target.value);
                this.state.currentPage = 1;
                this.render();
            });
        }

        // 筛选事件
        if (this.options.filterable) {
            this.container.querySelectorAll('.column-filter').forEach(filter => {
                filter.addEventListener('change', () => this.applyFilters());
            });
        }

        // 导出事件
        if (this.options.exportable) {
            this.container.querySelector('.export-csv-btn')?.addEventListener('click', () => this.exportCSV());
            this.container.querySelector('.export-excel-btn')?.addEventListener('click', () => this.exportExcel());
            this.container.querySelector('.export-pdf-btn')?.addEventListener('click', () => this.exportPDF());
        }
    }

    setData(data) {
        this.state.data = data;
        this.state.filteredData = [...data];
        this.render();
    }

    render() {
        this.renderTable();
        if (this.options.pagination) {
            this.renderPagination();
        }
    }

    renderTable() {
        const tbody = this.container.querySelector('.table-body');
        const data = this.getPaginatedData();
        
        if (data.length === 0) {
            tbody.innerHTML = this.createEmptyState();
            return;
        }

        let html = '';
        data.forEach(row => {
            const isSelected = this.state.selectedRows.has(row.id);
            html += `<tr class="${isSelected ? 'selected-row' : ''}" data-id="${row.id}">`;
            
            if (this.options.selectable) {
                html += `
                    <td>
                        <input type="checkbox" class="form-check-input row-checkbox" 
                               value="${row.id}" ${isSelected ? 'checked' : ''}>
                    </td>
                `;
            }
            
            this.options.columns.forEach(col => {
                const value = this.getCellValue(row, col);
                html += `<td>${value}</td>`;
            });
            
            html += '</tr>';
        });
        
        tbody.innerHTML = html;
        this.bindRowEvents();
    }

    bindRowEvents() {
        // 行选择事件
        this.container.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const id = parseInt(e.target.value);
                if (e.target.checked) {
                    this.state.selectedRows.add(id);
                } else {
                    this.state.selectedRows.delete(id);
                }
                this.updateRowSelection();
                this.options.onSelect(Array.from(this.state.selectedRows));
            });
        });

        // 行点击事件
        this.container.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('click', (e) => {
                if (!e.target.matches('input, button, a')) {
                    const id = parseInt(row.dataset.id);
                    this.options.onRowClick(id, this.state.data.find(item => item.id === id));
                }
            });
        });
    }

    getCellValue(row, column) {
        const value = row[column.field];
        if (column.render) {
            return column.render(value, row);
        }
        return value || '';
    }

    getSortIcon(field) {
        if (this.state.sortField === field) {
            return this.state.sortOrder === 'asc' 
                ? '<i class="fas fa-sort-up text-success ms-1"></i>'
                : '<i class="fas fa-sort-down text-danger ms-1"></i>';
        }
        return '<i class="fas fa-sort text-muted ms-1"></i>';
    }

    handleSort(field) {
        if (this.state.sortField === field) {
            this.state.sortOrder = this.state.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.state.sortField = field;
            this.state.sortOrder = 'asc';
        }
        
        this.sortData();
        this.render();
        this.options.onSort(field, this.state.sortOrder);
    }

    sortData() {
        this.state.filteredData.sort((a, b) => {
            const aVal = a[this.state.sortField];
            const bVal = b[this.state.sortField];
            
            if (aVal < bVal) return this.state.sortOrder === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.state.sortOrder === 'asc' ? 1 : -1;
            return 0;
        });
    }

    getPaginatedData() {
        if (!this.options.pagination) {
            return this.state.filteredData;
        }
        
        const start = (this.state.currentPage - 1) * this.options.pageSize;
        const end = start + this.options.pageSize;
        return this.state.filteredData.slice(start, end);
    }

    renderPagination() {
        const totalPages = Math.ceil(this.state.filteredData.length / this.options.pageSize);
        const paginationList = this.container.querySelector('.pagination-list');
        
        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${this.state.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.state.currentPage - 1}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        const startPage = Math.max(1, this.state.currentPage - 2);
        const endPage = Math.min(totalPages, this.state.currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === this.state.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }
        
        // 下一页
        html += `
            <li class="page-item ${this.state.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.state.currentPage + 1}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        paginationList.innerHTML = html;
        
        // 绑定分页事件
        paginationList.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(e.target.closest('a').dataset.page);
                if (page >= 1 && page <= totalPages) {
                    this.state.currentPage = page;
                    this.render();
                }
            });
        });
    }

    // 工具方法
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showLoading() {
        this.container.querySelector('.table-loading').style.display = 'block';
        this.container.querySelector('.table-body').style.display = 'none';
    }

    hideLoading() {
        this.container.querySelector('.table-loading').style.display = 'none';
        this.container.querySelector('.table-body').style.display = '';
    }

    // 导出方法
    exportCSV() {
        const data = this.state.selectedRows.size > 0 
            ? this.state.data.filter(row => this.state.selectedRows.has(row.id))
            : this.state.filteredData;
        
        // 实现CSV导出逻辑
        console.log('导出CSV', data);
    }

    exportExcel() {
        // 实现Excel导出逻辑
        console.log('导出Excel');
    }

    exportPDF() {
        // 实现PDF导出逻辑
        console.log('导出PDF');
    }

    // 公共API
    getSelectedRows() {
        return Array.from(this.state.selectedRows);
    }

    clearSelection() {
        this.state.selectedRows.clear();
        this.updateRowSelection();
    }

    refresh() {
        this.render();
    }

    updateRowSelection() {
        this.container.querySelectorAll('.row-checkbox').forEach(checkbox => {
            const id = parseInt(checkbox.value);
            checkbox.checked = this.state.selectedRows.has(id);
            const row = checkbox.closest('tr');
            row.classList.toggle('selected-row', checkbox.checked);
        });
        
        // 更新全选状态
        const selectAllCheckbox = this.container.querySelector('.select-all-checkbox');
        if (selectAllCheckbox) {
            const totalRows = this.getPaginatedData().length;
            const selectedRows = this.state.selectedRows.size;
            
            selectAllCheckbox.indeterminate = selectedRows > 0 && selectedRows < totalRows;
            selectAllCheckbox.checked = selectedRows > 0 && selectedRows === totalRows;
        }
    }
}

// 导出组件
window.AdvancedDataTable = AdvancedDataTable;
