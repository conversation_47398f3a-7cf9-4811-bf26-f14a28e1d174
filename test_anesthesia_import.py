#!/usr/bin/env python3
"""
测试麻醉方法清洗和导入功能
"""
import sys
import os
from pathlib import Path
import pandas as pd
import requests
import time

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def create_test_data_with_anesthesia():
    """创建包含各种麻醉方法的测试数据"""
    test_data = {
        '患者ID': ['P001', 'P002', 'P003', 'P004', 'P005', 'P006', 'P007', 'P008'],
        '患者姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
        '年龄': [45, 32, 67, 28, 55, 38, 42, 29],
        '性别': ['男', '女', '男', '女', '男', '女', '男', '女'],
        '手术日期': ['2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18', 
                   '2024-01-19', '2024-01-20', '2024-01-21', '2024-01-22'],
        '麻醉方法': [
            '全身麻醉（气管插管）',
            '硬膜外麻醉+局部麻醉',
            '神经阻滞麻醉（超声引导）',
            '静脉麻醉',
            '椎管内麻醉（腰硬联合）',
            '全身吸入麻醉',
            '蛛网膜下腔阻滞',
            '臂丛神经阻滞+局部麻醉'
        ],
        '手术类型': ['择期手术', '急诊手术', '日间手术', '择期手术', 
                   '急诊手术', '择期手术', '日间手术', '择期手术'],
        '科室': ['骨科', '普外科', '妇产科', '泌尿外科', 
               '神经外科', '胸外科', '眼科', '耳鼻喉科'],
        '麻醉医生': ['张医生', '李医生', '王医生', '赵医生', 
                   '钱医生', '孙医生', '周医生', '吴医生'],
        '手术医生': ['陈医生', '刘医生', '杨医生', '黄医生', 
                   '朱医生', '林医生', '何医生', '郭医生']
    }
    
    return pd.DataFrame(test_data)

def test_anesthesia_cleaning():
    """测试麻醉方法清洗功能"""
    print("=" * 60)
    print("测试麻醉方法清洗功能")
    print("=" * 60)
    
    # 导入清洗器
    from services.data_cleaner import AnesthesiaMethodCleaner
    
    cleaner = AnesthesiaMethodCleaner()
    
    test_methods = [
        '全身麻醉（气管插管）',
        '硬膜外麻醉+局部麻醉',
        '神经阻滞麻醉（超声引导）',
        '静脉麻醉',
        '椎管内麻醉（腰硬联合）',
        '全身吸入麻醉',
        '蛛网膜下腔阻滞',
        '臂丛神经阻滞+局部麻醉',
        '静吸复合麻醉',
        '骶管阻滞',
        '坐骨神经阻滞',
        '股神经阻滞'
    ]
    
    print("原始麻醉方法 → 清洗结果:")
    print("-" * 60)
    
    for method in test_methods:
        result = cleaner.clean_anesthesia_method(method)
        print(f"原始: {method}")
        print(f"  清洗后: {result['cleaned']}")
        print(f"  主要方法: {result['primary_anesthesia']}")
        print(f"  复合方法: {result['compound_anesthesia']}")
        print()

def test_data_import():
    """测试数据导入功能"""
    print("=" * 60)
    print("测试数据导入功能")
    print("=" * 60)
    
    # 创建测试数据
    df = create_test_data_with_anesthesia()
    test_file = "test_anesthesia_data.xlsx"
    df.to_excel(test_file, index=False)
    print(f"创建测试文件: {test_file}")
    print(f"测试数据包含 {len(df)} 行记录")
    print()
    
    print("原始麻醉方法:")
    for i, method in enumerate(df['麻醉方法'], 1):
        print(f"{i}. {method}")
    print()
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    try:
        # 测试文件上传和预览
        print("1. 测试文件预览...")
        with open(test_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:5000/api/preview-data', files=files, timeout=30)
        
        if response.status_code == 200:
            preview_data = response.json()
            if preview_data.get('success'):
                print("✅ 文件预览成功")
                print(f"   预览行数: {len(preview_data.get('preview_data', []))}")
                
                # 检查预览数据中的麻醉方法
                preview_records = preview_data.get('preview_data', [])
                if preview_records:
                    print("   预览中的麻醉方法:")
                    for i, record in enumerate(preview_records[:3], 1):
                        anesthesia = record.get('麻醉方法', 'N/A')
                        print(f"     {i}. {anesthesia}")
            else:
                print(f"❌ 文件预览失败: {preview_data.get('error')}")
                return
        else:
            print(f"❌ 文件预览请求失败: {response.status_code}")
            return
        
        print()
        
        # 测试数据导入
        print("2. 测试数据导入...")

        # 重新上传文件进行导入
        with open(test_file, 'rb') as f:
            files = {'file': f}
            data = {'privacy_enabled': 'false'}
            response = requests.post('http://localhost:5000/api/import-data',
                                   files=files, data=data, timeout=60)
        
        if response.status_code == 200:
            import_result = response.json()
            if import_result.get('success'):
                print("✅ 数据导入成功")
                print(f"   导入记录数: {import_result.get('imported_count', 0)}")
                print(f"   批次ID: {import_result.get('batch_id', 'N/A')}")
            else:
                print(f"❌ 数据导入失败: {import_result.get('error')}")
                return
        else:
            print(f"❌ 数据导入请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            return
        
        print()
        
        # 验证导入结果
        print("3. 验证导入结果...")
        response = requests.get('http://localhost:5000/api/surgery-records?page=1&page_size=20', timeout=30)
        
        if response.status_code == 200:
            records_data = response.json()
            if records_data.get('success'):
                records = records_data.get('records', [])
                print(f"✅ 获取到 {len(records)} 条手术记录")
                
                # 检查麻醉方法是否被正确清洗
                print("\n   导入后的麻醉方法:")
                anesthesia_methods = set()
                for record in records[-8:]:  # 查看最新的8条记录
                    method = record.get('anesthesia_method', 'N/A')
                    primary = record.get('primary_anesthesia', 'N/A')
                    compound = record.get('compound_anesthesia', 'N/A')
                    anesthesia_methods.add(primary)
                    
                    print(f"     原始: {method}")
                    print(f"     主要: {primary}")
                    print(f"     复合: {compound}")
                    print()
                
                print(f"   标准化后的麻醉方法类型: {len(anesthesia_methods)} 种")
                print(f"   标准化方法: {', '.join(sorted(anesthesia_methods))}")
                
            else:
                print(f"❌ 获取手术记录失败: {records_data.get('error')}")
        else:
            print(f"❌ 获取手术记录请求失败: {response.status_code}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
    
    finally:
        # 清理测试文件
        try:
            os.remove(test_file)
            print(f"\n清理测试文件: {test_file}")
        except:
            pass

def main():
    """主函数"""
    print("麻醉方法清洗和导入测试")
    print("=" * 80)
    
    # 测试清洗功能
    test_anesthesia_cleaning()
    
    # 测试导入功能
    test_data_import()
    
    print("\n" + "=" * 80)
    print("测试完成！")

if __name__ == "__main__":
    main()
