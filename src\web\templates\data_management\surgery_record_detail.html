{% extends "base.html" %}

{% block title %}手术记录详情 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item"><a href="/data-management">数据管理</a></li>
            <li class="breadcrumb-item"><a href="/data-management/surgery-records">手术记录</a></li>
            <li class="breadcrumb-item active" aria-current="page">记录详情</li>
        </ol>
    </nav>
{% endblock %}

{% block content %}
<!-- 操作工具栏 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-2">
                        <button class="btn btn-secondary" onclick="history.back()">
                            <i class="fas fa-arrow-left me-2"></i>返回列表
                        </button>
                        <button class="btn btn-primary" id="editBtn">
                            <i class="fas fa-edit me-2"></i>编辑记录
                        </button>
                        <button class="btn btn-success" id="printBtn">
                            <i class="fas fa-print me-2"></i>打印
                        </button>
                        <button class="btn btn-info" id="exportBtn">
                            <i class="fas fa-download me-2"></i>导出
                        </button>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left me-2"></i>上一条
                        </button>
                        <button class="btn btn-outline-secondary" id="nextBtn" disabled>
                            下一条<i class="fas fa-chevron-right ms-2"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 记录详情 -->
<div class="row">
    <!-- 基本信息 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user me-2"></i>患者信息</h6>
            </div>
            <div class="card-body">
                <div id="patientInfo">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载患者信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 手术信息 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-procedures me-2"></i>手术信息</h6>
            </div>
            <div class="card-body">
                <div id="surgeryInfo">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载手术信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 麻醉信息 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-syringe me-2"></i>麻醉信息</h6>
            </div>
            <div class="card-body">
                <div id="anesthesiaInfo">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载麻醉信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 医护信息 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user-md me-2"></i>医护信息</h6>
            </div>
            <div class="card-body">
                <div id="staffInfo">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载医护信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 备注和并发症 -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-notes-medical me-2"></i>备注和并发症</h6>
            </div>
            <div class="card-body">
                <div id="notesInfo">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载备注信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作历史 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-history me-2"></i>操作历史</h6>
            </div>
            <div class="card-body">
                <div id="operationHistory">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载操作历史...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const recordId = getRecordIdFromUrl();
    
    if (recordId) {
        loadRecordDetail(recordId);
        loadNavigationInfo(recordId);
    } else {
        showAlert('无效的记录ID', 'danger');
        setTimeout(() => {
            window.location.href = '/data-management/surgery-records';
        }, 2000);
    }
    
    // 绑定事件
    document.getElementById('editBtn').addEventListener('click', function() {
        window.location.href = `/data-management/surgery-records/${recordId}/edit`;
    });
    
    document.getElementById('printBtn').addEventListener('click', function() {
        window.print();
    });
    
    document.getElementById('exportBtn').addEventListener('click', function() {
        window.open(`/api/surgery-records/${recordId}/export`, '_blank');
    });
});

function getRecordIdFromUrl() {
    const pathParts = window.location.pathname.split('/');
    return pathParts[pathParts.length - 1];
}

function loadRecordDetail(recordId) {
    fetch(`/api/surgery-records/${recordId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderRecordDetail(data.record);
        } else {
            showAlert('加载记录详情失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('加载记录详情失败: ' + error.message, 'danger');
    });
}

function renderRecordDetail(record) {
    // 患者信息
    document.getElementById('patientInfo').innerHTML = `
        <div class="row">
            <div class="col-6">
                <strong>患者姓名:</strong><br>
                <span class="text-primary">${record.patient_name || '未知'}</span>
            </div>
            <div class="col-6">
                <strong>患者ID:</strong><br>
                <span class="text-muted">${record.patient_id || '未知'}</span>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-6">
                <strong>年龄:</strong><br>
                <span>${record.age || '未知'}</span>
            </div>
            <div class="col-6">
                <strong>性别:</strong><br>
                <span>${record.gender || '未知'}</span>
            </div>
        </div>
    `;
    
    // 手术信息
    document.getElementById('surgeryInfo').innerHTML = `
        <div class="row">
            <div class="col-6">
                <strong>手术日期:</strong><br>
                <span class="text-primary">${record.surgery_date}</span>
            </div>
            <div class="col-6">
                <strong>手术类型:</strong><br>
                <span>${record.surgery_type}</span>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-6">
                <strong>科室:</strong><br>
                <span class="badge bg-info">${record.department}</span>
            </div>
            <div class="col-6">
                <strong>手术时长:</strong><br>
                <span>${record.duration_minutes} 分钟</span>
            </div>
        </div>
    `;
    
    // 麻醉信息
    document.getElementById('anesthesiaInfo').innerHTML = `
        <div class="row">
            <div class="col-6">
                <strong>麻醉方法:</strong><br>
                <span class="badge bg-primary">${record.anesthesia_method}</span>
            </div>
            <div class="col-6">
                <strong>主要麻醉:</strong><br>
                <span>${record.primary_anesthesia || '未指定'}</span>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-6">
                <strong>复合麻醉:</strong><br>
                <span>${record.compound_anesthesia || '无'}</span>
            </div>
            <div class="col-6">
                <strong>ASA分级:</strong><br>
                <span class="badge bg-${getAsaBadgeColor(record.asa_grade)}">${record.asa_grade}</span>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12">
                <strong>术后镇痛:</strong><br>
                <span class="badge bg-${record.postoperative_analgesia === '是' ? 'success' : 'secondary'}">
                    ${record.postoperative_analgesia || '未记录'}
                </span>
            </div>
        </div>
    `;
    
    // 医护信息
    document.getElementById('staffInfo').innerHTML = `
        <div class="row">
            <div class="col-6">
                <strong>麻醉医师:</strong><br>
                <span class="text-success">${record.anesthesiologist || '未指定'}</span>
            </div>
            <div class="col-6">
                <strong>手术医师:</strong><br>
                <span class="text-info">${record.surgeon || '未指定'}</span>
            </div>
        </div>
    `;
    
    // 备注和并发症
    document.getElementById('notesInfo').innerHTML = `
        <div class="row">
            <div class="col-6">
                <strong>并发症:</strong><br>
                <span class="badge bg-${record.complications === '无' ? 'success' : 'warning'}">
                    ${record.complications || '无'}
                </span>
            </div>
            <div class="col-6">
                <strong>创建时间:</strong><br>
                <span class="text-muted">${new Date(record.created_at).toLocaleString()}</span>
            </div>
        </div>
        ${record.notes ? `
            <hr>
            <div class="row">
                <div class="col-12">
                    <strong>备注:</strong><br>
                    <div class="bg-light p-3 rounded">
                        ${record.notes}
                    </div>
                </div>
            </div>
        ` : ''}
    `;
    
    // 操作历史（模拟数据）
    document.getElementById('operationHistory').innerHTML = `
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-marker bg-primary"></div>
                <div class="timeline-content">
                    <h6 class="timeline-title">记录创建</h6>
                    <p class="timeline-text">记录已创建并导入系统</p>
                    <small class="text-muted">${new Date(record.created_at).toLocaleString()}</small>
                </div>
            </div>
            ${record.updated_at && record.updated_at !== record.created_at ? `
                <div class="timeline-item">
                    <div class="timeline-marker bg-success"></div>
                    <div class="timeline-content">
                        <h6 class="timeline-title">记录更新</h6>
                        <p class="timeline-text">记录信息已更新</p>
                        <small class="text-muted">${new Date(record.updated_at).toLocaleString()}</small>
                    </div>
                </div>
            ` : ''}
        </div>
    `;
}

function loadNavigationInfo(recordId) {
    fetch(`/api/surgery-records/${recordId}/navigation`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            if (data.prev_id) {
                prevBtn.disabled = false;
                prevBtn.onclick = () => {
                    window.location.href = `/data-management/surgery-records/${data.prev_id}`;
                };
            }
            
            if (data.next_id) {
                nextBtn.disabled = false;
                nextBtn.onclick = () => {
                    window.location.href = `/data-management/surgery-records/${data.next_id}`;
                };
            }
        }
    })
    .catch(error => {
        console.error('加载导航信息失败:', error);
    });
}

function getAsaBadgeColor(grade) {
    const colors = {
        'I': 'success',
        'II': 'info',
        'III': 'warning',
        'IV': 'danger'
    };
    return colors[grade] || 'secondary';
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    color: #6c757d;
}

@media print {
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .btn, .breadcrumb {
        display: none !important;
    }
}
</style>
{% endblock %}
