"""
数据处理服务
"""
import uuid
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

from database.models import DatabaseManager
from utils.data_privacy import DataPrivacyProcessor, get_default_privacy_config
from utils.logger import logger
from utils.performance import timing_decorator, PerformanceContext
from .data_pipeline import DataProcessingPipeline, create_default_config


class DataImportService:
    """数据导入服务"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.privacy_processor = DataPrivacyProcessor()
        self.processing_pipeline = DataProcessingPipeline()
        self.upload_dir = Path("uploads")
        self.upload_dir.mkdir(exist_ok=True)
        logger.info("数据导入服务初始化完成")
    
    @timing_decorator
    def import_excel_file(self, file_path: str, privacy_enabled: bool = True, 
                         privacy_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        导入Excel文件到数据库
        
        Args:
            file_path: Excel文件路径
            privacy_enabled: 是否启用数据脱敏
            privacy_config: 脱敏配置
            
        Returns:
            导入结果
        """
        batch_id = str(uuid.uuid4())
        filename = Path(file_path).name
        
        logger.info(f"开始导入文件: {filename}, 批次ID: {batch_id}")
        
        try:
            # 创建导入批次记录
            batch_data = {
                'id': batch_id,
                'filename': filename,
                'privacy_enabled': privacy_enabled,
                'status': 'processing'
            }
            self.db.create_import_batch(batch_data)
            
            # 读取Excel文件
            with PerformanceContext("读取Excel文件"):
                df = pd.read_excel(file_path)
                logger.info(f"读取到 {len(df)} 条记录")
            
            # 数据清洗和标准化
            with PerformanceContext("数据清洗"):
                cleaned_df = self._clean_data(df)
            
            # 数据脱敏
            if privacy_enabled:
                with PerformanceContext("数据脱敏"):
                    if privacy_config is None:
                        privacy_config = get_default_privacy_config()
                    cleaned_df = self.privacy_processor.anonymize_dataframe(cleaned_df, privacy_config)
            
            # 导入数据到数据库
            with PerformanceContext("导入数据库"):
                import_result = self._import_to_database(cleaned_df, batch_id, privacy_enabled)
            
            # 更新批次状态
            self.db.update_import_batch(batch_id, {
                'total_records': len(df),
                'new_records': import_result['new_records'],
                'duplicate_records': import_result['duplicate_records'],
                'error_records': import_result['error_records'],
                'status': 'completed',
                'completed_at': datetime.now().isoformat()
            })
            
            # 记录操作日志
            self.db.log_operation(
                'data_import',
                f"导入文件 {filename}: {import_result['new_records']} 新记录, "
                f"{import_result['duplicate_records']} 重复记录"
            )
            
            result = {
                'success': True,
                'batch_id': batch_id,
                'filename': filename,
                'total_records': len(df),
                'new_records': import_result['new_records'],
                'duplicate_records': import_result['duplicate_records'],
                'error_records': import_result['error_records'],
                'privacy_enabled': privacy_enabled,
                'privacy_summary': self.privacy_processor.get_privacy_summary() if privacy_enabled else None
            }
            
            logger.info(f"文件导入完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"文件导入失败: {str(e)}")
            
            # 更新批次状态为失败
            self.db.update_import_batch(batch_id, {
                'status': 'failed',
                'error_message': str(e),
                'completed_at': datetime.now().isoformat()
            })
            
            return {
                'success': False,
                'batch_id': batch_id,
                'error': str(e)
            }
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        # 复制数据避免修改原始数据
        cleaned_df = df.copy()
        
        # 标准化列名
        column_mapping = {
            # 患者基本信息
            '患者ID': 'patient_id',
            '住院号': 'patient_id',
            '病案号': 'patient_id',
            '患者姓名': 'patient_name',
            '姓名': 'patient_name',
            '性别': 'gender',
            '年龄': 'age',

            # 手术信息
            '手术日期': 'surgery_date',
            '手术室': 'operating_room',
            '手术间': 'operating_room_number',
            '手术名称': 'surgery_name',
            '手术类型': 'surgery_type',
            '手术级别': 'surgery_level',
            '切口等级': 'incision_level',
            '术前诊断': 'preoperative_diagnosis',

            # 麻醉信息
            '麻醉方法': 'anesthesia_method',
            '麻醉方式': 'anesthesia_method',
            '主麻': 'anesthesiologist',
            '副麻': 'assistant_anesthesiologist',
            '麻醉医生': 'anesthesiologist',
            '麻醉助手': 'anesthesia_assistant',
            '术后镇痛': 'postoperative_analgesia',

            # 手术人员
            '手术医生': 'surgeon',
            '一助': 'first_assistant',
            '二助': 'second_assistant',
            '三助': 'third_assistant',
            '灌注医生': 'perfusionist',
            '洗手护士1': 'scrub_nurse_1',
            '洗手护士2': 'scrub_nurse_2',
            '巡回护士1': 'circulating_nurse_1',
            '巡回护士2': 'circulating_nurse_2',
            '接替器械护士': 'relief_scrub_nurse',
            '接替巡回护士': 'relief_circulating_nurse',

            # 时间信息
            '入室时间': 'room_entry_time',
            '麻醉开始时间': 'anesthesia_start_time',
            '手术开始时间': 'surgery_start_time',
            '手术结束时间': 'surgery_end_time',
            '麻醉结束时间': 'anesthesia_end_time',
            '出室时间': 'room_exit_time',
            '入PACU时间': 'pacu_entry_time',
            '出PACU时间': 'pacu_exit_time',

            # 其他信息
            '临床科室': 'department',
            '体位': 'position',
            '压疮评估': 'pressure_ulcer_assessment',
            '手术部位标识': 'surgical_site_marking',
            '术中导尿': 'intraoperative_catheterization',
            '尿管护理': 'catheter_care',
            '病理标本': 'pathological_specimen',
            '科室': 'department',
            '手术时长': 'duration_minutes',
            'ASA分级': 'asa_grade',
            '并发症': 'complications',
            '备注': 'notes',
            '手机号': 'phone',
            '联系电话': 'phone',
            '身份证号': 'id_card'
        }
        
        # 重命名列
        for old_name, new_name in column_mapping.items():
            if old_name in cleaned_df.columns:
                cleaned_df = cleaned_df.rename(columns={old_name: new_name})
        
        # 处理麻醉方法 - 使用新的清洗器
        if 'anesthesia_method' in cleaned_df.columns:
            logger.info("开始清洗麻醉方法...")

            # 使用新的麻醉方法清洗器
            anesthesia_results = cleaned_df['anesthesia_method'].apply(
                self.processing_pipeline.cleaner.anesthesia_cleaner.clean_anesthesia_method
            )

            # 展开清洗结果
            cleaned_df['anesthesia_method_cleaned'] = anesthesia_results.apply(lambda x: x['cleaned'])
            cleaned_df['primary_anesthesia'] = anesthesia_results.apply(lambda x: x['primary_anesthesia'])
            cleaned_df['compound_anesthesia'] = anesthesia_results.apply(lambda x: x['compound_anesthesia'])
            cleaned_df['primary_anesthesia_raw'] = anesthesia_results.apply(lambda x: x['primary_raw'])
            cleaned_df['compound_anesthesia_raw'] = anesthesia_results.apply(lambda x: x['compound_raw'])

            # 更新主要的麻醉方法字段
            cleaned_df['anesthesia_method'] = cleaned_df['primary_anesthesia']

            # 统计清洗结果
            original_unique = cleaned_df['anesthesia_method'].nunique()
            logger.info(f"麻醉方法清洗完成，标准化为 {original_unique} 种方法")
        
        # 处理日期
        if 'surgery_date' in cleaned_df.columns:
            cleaned_df['surgery_date'] = pd.to_datetime(cleaned_df['surgery_date'], errors='coerce').dt.strftime('%Y-%m-%d')
        
        # 处理数值字段
        if 'duration_minutes' in cleaned_df.columns:
            cleaned_df['duration_minutes'] = pd.to_numeric(cleaned_df['duration_minutes'], errors='coerce')
        
        # 填充空值
        cleaned_df = cleaned_df.fillna('')
        
        return cleaned_df
    

    
    def _import_to_database(self, df: pd.DataFrame, batch_id: str, privacy_enabled: bool) -> Dict[str, int]:
        """导入数据到数据库"""
        new_records = 0
        duplicate_records = 0
        error_records = 0
        
        for index, row in df.iterrows():
            try:
                # 准备患者数据 - 包含原始数据和脱敏数据
                original_patient_id = row.get('patient_id', '')
                original_patient_name = row.get('patient_name', '')
                original_phone = row.get('phone', '')
                original_id_card = row.get('id_card', '')

                # 使用正确的脱敏方法（*替换）
                patient_data = {
                    # 原始数据
                    'original_id': original_patient_id,
                    'original_name': original_patient_name,
                    # 脱敏数据
                    'patient_id': self.privacy_processor.mask_hospital_id(original_patient_id),  # 使用*替换的方法
                    'name': self.privacy_processor.mask_name(original_patient_name),  # 使用*替换的方法
                    'phone': self.privacy_processor.mask_phone_number_consistent(original_phone),
                    'id_card': self.privacy_processor.mask_id_card(original_id_card),  # 使用现有方法
                    'age': row.get('age'),                       # 年龄（非敏感）
                    'gender': row.get('gender', '')              # 性别（非敏感）
                }

                # 查找或创建患者
                patient_id = self.db.find_or_create_patient(patient_data)
                
                # 准备手术记录数据
                record_data = {
                    'patient_id': patient_id,
                    'surgery_date': str(row.get('surgery_date', '')),
                    'anesthesia_method': str(row.get('anesthesia_method', '')),
                    'primary_anesthesia': str(row.get('primary_anesthesia', '')),
                    'compound_anesthesia': str(row.get('compound_anesthesia', '')),
                    'surgery_type': str(row.get('surgery_type', '')),
                    'postoperative_analgesia': str(row.get('postoperative_analgesia', '')),
                    'anesthesiologist': str(row.get('anesthesiologist', '')),
                    'surgeon': str(row.get('surgeon', '')),
                    'department': str(row.get('department', '')),
                    'duration_minutes': int(row.get('duration_minutes', 0)) if pd.notna(row.get('duration_minutes')) else None,
                    'asa_grade': str(row.get('asa_grade', '')),
                    'complications': str(row.get('complications', '')),
                    'notes': str(row.get('notes', '')),
                    'import_batch_id': batch_id
                }
                
                # 生成数据哈希
                hash_data = {
                    'original_id': patient_data['original_id'],
                    'surgery_date': record_data['surgery_date'],
                    'anesthesia_method': record_data['anesthesia_method'],
                    'surgery_type': record_data['surgery_type']
                }
                data_hash = self.db.generate_data_hash(hash_data)
                record_data['data_hash'] = data_hash
                
                # 检查重复
                if self.db.check_duplicate(data_hash):
                    duplicate_records += 1
                    logger.debug(f"发现重复记录: {data_hash}")
                else:
                    # 插入新记录
                    self.db.insert_surgery_record(record_data)
                    new_records += 1
                
            except Exception as e:
                logger.error(f"处理第 {index + 1} 行数据时出错: {str(e)}")
                error_records += 1
        
        return {
            'new_records': new_records,
            'duplicate_records': duplicate_records,
            'error_records': error_records
        }


    def process_file_with_pipeline(self, file_path: str,
                                 clean_data: bool = True,
                                 anonymize_data: bool = True,
                                 import_to_db: bool = True,
                                 output_dir: str = "output") -> Dict[str, Any]:
        """
        使用新的处理管道处理文件

        Args:
            file_path: 文件路径
            clean_data: 是否进行数据清洗
            anonymize_data: 是否进行数据脱敏
            import_to_db: 是否导入到数据库
            output_dir: 输出目录

        Returns:
            处理结果
        """
        logger.info(f"使用处理管道处理文件: {file_path}")

        try:
            # 1. 验证文件
            validation_result = self.processing_pipeline.validate_file(file_path)
            if not validation_result['is_valid']:
                return {
                    'success': False,
                    'error': f"文件验证失败: {validation_result['errors']}",
                    'validation_result': validation_result
                }

            # 2. 获取默认配置
            config = create_default_config()

            # 3. 处理文件
            processing_result = self.processing_pipeline.process_file(
                file_path=file_path,
                output_dir=output_dir,
                clean_data=clean_data,
                anonymize_data=anonymize_data,
                field_mapping=config['field_mapping'],
                anonymize_config=config['anonymize_config'],
                save_intermediate=True
            )

            # 4. 如果需要，导入到数据库
            if import_to_db and processing_result.get('final_file'):
                import_result = self.import_excel_file(
                    processing_result['final_file'],
                    privacy_enabled=False  # 已经脱敏过了
                )
                processing_result['import_result'] = import_result

            return {
                'success': True,
                'processing_result': processing_result,
                'validation_result': validation_result
            }

        except Exception as e:
            logger.error(f"处理管道执行失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
