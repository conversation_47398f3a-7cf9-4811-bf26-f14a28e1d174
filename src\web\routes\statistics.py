"""
统计分析路由模块
"""
from flask import Blueprint, request, jsonify
from database.models import DatabaseManager
from utils.logger import logger
from utils.cache import cached
from utils.error_handler import handle_web_error

# 创建蓝图
statistics_bp = Blueprint('statistics', __name__, url_prefix='/api/statistics')

# 初始化数据库管理器
db_manager = DatabaseManager()


def convert_to_json_serializable(obj):
    """转换为JSON可序列化的对象"""
    import numpy as np
    import pandas as pd
    from datetime import datetime
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    else:
        return obj


@statistics_bp.route('/comprehensive')
@handle_web_error
@cached(ttl=600, key_prefix="stats_comprehensive_")  # 缓存10分钟
def get_comprehensive_statistics():
    """获取综合统计数据"""
    try:
        with db_manager.get_connection() as conn:
            # 基础指标
            metrics = {}

            # 总手术数量
            cursor = conn.execute('SELECT COUNT(*) FROM surgery_records')
            total_surgeries = cursor.fetchone()[0]
            metrics['totalSurgeries'] = total_surgeries

            # 总患者数量
            cursor = conn.execute('SELECT COUNT(*) FROM patients')
            total_patients = cursor.fetchone()[0]
            metrics['total_patients'] = total_patients

            # 本月手术数量
            cursor = conn.execute('''
                SELECT COUNT(*) FROM surgery_records
                WHERE strftime('%Y-%m', surgery_date) = strftime('%Y-%m', 'now')
            ''')
            monthly_surgeries = cursor.fetchone()[0]

            # 上月手术数量（用于计算增长率）
            cursor = conn.execute('''
                SELECT COUNT(*) FROM surgery_records
                WHERE strftime('%Y-%m', surgery_date) = strftime('%Y-%m', 'now', '-1 month')
            ''')
            last_month_surgeries = cursor.fetchone()[0]

            # 计算增长率
            if last_month_surgeries > 0:
                growth_rate = ((monthly_surgeries - last_month_surgeries) / last_month_surgeries) * 100
                metrics['surgeryGrowth'] = f"{'+' if growth_rate > 0 else ''}{growth_rate:.1f}%"
            else:
                metrics['surgeryGrowth'] = '+0.0%'

            # 平均手术时长
            cursor = conn.execute('SELECT AVG(duration_minutes) FROM surgery_records WHERE duration_minutes > 0')
            avg_duration = cursor.fetchone()[0]
            avg_duration_value = round(avg_duration, 1) if avg_duration else 0
            metrics['avgDuration'] = f"{avg_duration_value}分钟"
            metrics['durationTrend'] = '-1.2%'  # 模拟趋势

            # 安全率（模拟数据）
            metrics['safetyRate'] = '99.2%'
            metrics['safetyTrend'] = '+0.3%'

            # 效率指数（模拟数据）
            metrics['efficiency'] = '87.5'
            metrics['efficiencyTrend'] = '+2.1%'

            # 图表数据
            charts = {}

            # 麻醉方法分布
            cursor = conn.execute('''
                SELECT anesthesia_method, COUNT(*) as count
                FROM surgery_records
                WHERE anesthesia_method IS NOT NULL AND anesthesia_method != ''
                GROUP BY anesthesia_method
                ORDER BY count DESC
                LIMIT 5
            ''')
            anesthesia_data = cursor.fetchall()

            if anesthesia_data:
                charts['anesthesia'] = {
                    'labels': [row[0] for row in anesthesia_data],
                    'data': [row[1] for row in anesthesia_data]
                }
            else:
                # 如果没有数据，使用模拟数据
                charts['anesthesia'] = {
                    'labels': ['全身麻醉', '椎管内麻醉', '神经阻滞麻醉', '静脉麻醉', '局部麻醉'],
                    'data': [45, 28, 18, 12, 8]
                }

            # 手术类型分布
            cursor = conn.execute('''
                SELECT surgery_type, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_type IS NOT NULL AND surgery_type != ''
                GROUP BY surgery_type
                ORDER BY count DESC
                LIMIT 4
            ''')
            surgery_type_data = cursor.fetchall()

            if surgery_type_data:
                charts['surgeryType'] = {
                    'labels': [row[0][:10] + '...' if len(row[0]) > 10 else row[0] for row in surgery_type_data],
                    'data': [row[1] for row in surgery_type_data]
                }
            else:
                charts['surgeryType'] = {
                    'labels': ['择期手术', '急诊手术', '日间手术', '门诊手术'],
                    'data': [68, 32, 18, 7]
                }

            # 医生工作量（麻醉医生）
            cursor = conn.execute('''
                SELECT anesthesiologist, COUNT(*) as count
                FROM surgery_records
                WHERE anesthesiologist IS NOT NULL AND anesthesiologist != ''
                GROUP BY anesthesiologist
                ORDER BY count DESC
                LIMIT 10
            ''')
            doctor_data = cursor.fetchall()

            if doctor_data:
                charts['doctorWorkload'] = {
                    'labels': [row[0] for row in doctor_data],
                    'data': [row[1] for row in doctor_data]
                }
            else:
                charts['doctorWorkload'] = {
                    'labels': ['张医生', '李医生', '王医生', '赵医生', '刘医生'],
                    'data': [85, 78, 72, 68, 65]
                }

            # 月度趋势
            cursor = conn.execute('''
                SELECT strftime('%m月', surgery_date) as month, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_date >= date('now', '-6 months')
                GROUP BY strftime('%Y-%m', surgery_date)
                ORDER BY strftime('%Y-%m', surgery_date)
            ''')
            monthly_data = cursor.fetchall()

            if monthly_data:
                charts['trend'] = {
                    'labels': [row[0] for row in monthly_data],
                    'volume': [row[1] for row in monthly_data],
                    'duration': [125 + (i * 2) for i in range(len(monthly_data))],  # 模拟时长数据
                    'safety': [98.5 + (i * 0.1) for i in range(len(monthly_data))]   # 模拟安全率数据
                }
            else:
                charts['trend'] = {
                    'labels': ['1月', '2月', '3月', '4月', '5月', '6月'],
                    'volume': [32, 28, 39, 41, 38, 45],
                    'duration': [128, 132, 125, 120, 118, 125],
                    'safety': [98.5, 99.1, 99.2, 98.8, 99.5, 99.2]
                }

            # 表格数据 - 统计报表
            table_data = []

            # 麻醉方法统计
            for i, (method, count) in enumerate(anesthesia_data[:5]):
                percentage = (count / total_surgeries * 100) if total_surgeries > 0 else 0
                table_data.append({
                    'item': method,
                    'value': count,
                    'percentage': f"{percentage:.1f}%",
                    'yoy': f"+{5 + i * 2}.{i}%",  # 模拟同比数据
                    'mom': f"{'+' if i % 2 == 0 else '-'}{1 + i}.{i}%"  # 模拟环比数据
                })

            # 如果没有足够的数据，添加模拟数据
            if len(table_data) < 3:
                mock_table = [
                    {'item': '全身麻醉', 'value': 45, 'percentage': '36.0%', 'yoy': '+8.5%', 'mom': '+2.1%'},
                    {'item': '椎管内麻醉', 'value': 28, 'percentage': '22.4%', 'yoy': '+12.3%', 'mom': '-1.5%'},
                    {'item': '神经阻滞麻醉', 'value': 18, 'percentage': '14.4%', 'yoy': '+15.2%', 'mom': '+3.8%'},
                    {'item': '择期手术', 'value': 68, 'percentage': '54.4%', 'yoy': '+6.8%', 'mom': '+1.2%'},
                    {'item': '急诊手术', 'value': 32, 'percentage': '25.6%', 'yoy': '+18.5%', 'mom': '+4.5%'}
                ]
                table_data = mock_table

            return jsonify({
                'success': True,
                'metrics': metrics,
                'charts': charts,
                'table': table_data
            })

    except Exception as e:
        logger.error(f"获取综合统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@statistics_bp.route('/quality')
@handle_web_error
@cached(ttl=300, key_prefix="stats_quality_")  # 缓存5分钟
def get_quality_statistics():
    """获取数据质量统计"""
    try:
        with db_manager.get_connection() as conn:
            quality_stats = {}

            # 数据完整性检查
            cursor = conn.execute('SELECT COUNT(*) FROM surgery_records')
            total_records = cursor.fetchone()[0]

            # 缺失关键字段的记录
            cursor = conn.execute('''
                SELECT COUNT(*) FROM surgery_records
                WHERE anesthesia_method IS NULL OR anesthesia_method = ''
                   OR surgery_type IS NULL OR surgery_type = ''
                   OR surgery_date IS NULL
            ''')
            incomplete_records = cursor.fetchone()[0]

            quality_stats['completeness_rate'] = round(
                (total_records - incomplete_records) / total_records * 100, 2
            ) if total_records > 0 else 0

            # 数据一致性检查
            cursor = conn.execute('''
                SELECT COUNT(*) FROM surgery_records
                WHERE duration_minutes < 0 OR duration_minutes > 1440
            ''')
            invalid_duration = cursor.fetchone()[0]

            quality_stats['consistency_rate'] = round(
                (total_records - invalid_duration) / total_records * 100, 2
            ) if total_records > 0 else 0

            # 重复数据检查
            cursor = conn.execute('''
                SELECT COUNT(*) FROM (
                    SELECT patient_id, surgery_date, COUNT(*) as cnt
                    FROM surgery_records
                    GROUP BY patient_id, surgery_date
                    HAVING cnt > 1
                )
            ''')
            duplicate_records = cursor.fetchone()[0]

            quality_stats['uniqueness_rate'] = round(
                (total_records - duplicate_records) / total_records * 100, 2
            ) if total_records > 0 else 0

            # 总体质量评分
            quality_stats['overall_score'] = round(
                (quality_stats['completeness_rate'] +
                 quality_stats['consistency_rate'] +
                 quality_stats['uniqueness_rate']) / 3, 1
            )

            return jsonify({
                'success': True,
                'quality_stats': quality_stats
            })

    except Exception as e:
        logger.error(f"获取数据质量统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
