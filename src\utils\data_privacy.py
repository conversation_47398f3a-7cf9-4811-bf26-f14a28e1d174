"""
数据脱敏处理模块
"""
import re
import hashlib
import random
import string
from typing import Any, Dict, List, Optional
import pandas as pd
from utils.logger import logger


class DataPrivacyProcessor:
    """数据脱敏处理器"""
    
    def __init__(self, secret_key: str = "anesthesia_qc_secure_2024"):
        self.secret_key = secret_key
        self.name_mapping = {}  # 姓名映射缓存
        self.id_mapping = {}    # ID映射缓存
        self.phone_mapping = {}  # 电话映射缓存
        self.anonymization_stats = {}  # 脱敏统计

    def _generate_consistent_hash(self, data: str) -> str:
        """生成一致性哈希 - 确保相同输入产生相同输出"""
        combined = f"{self.secret_key}:{data}"
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()
        
    def mask_name(self, original_name: str) -> str:
        """
        使用*号脱敏姓名：
        - 单字：保持不变
        - 两字：保留第一个字，第二个字用*替换 (张三 → 张*)
        - 三字：保留第一个字和最后一个字，中间用*替换 (张三文 → 张*文)
        - 四字及以上：保留第一个字和最后一个字，中间全部用*替换

        Args:
            original_name: 原始姓名

        Returns:
            脱敏后的姓名
        """
        if pd.isna(original_name) or not str(original_name).strip():
            return original_name

        original_name = str(original_name).strip()

        # 如果已经映射过，直接返回
        if original_name in self.name_mapping:
            return self.name_mapping[original_name]

        # 姓名脱敏规则
        if len(original_name) == 0:
            masked_name = original_name
        elif len(original_name) == 1:
            # 单字姓名保持不变
            masked_name = original_name
        elif len(original_name) == 2:
            # 两字姓名：保留第一个字，第二个字用*替换
            masked_name = original_name[0] + '*'
        elif len(original_name) == 3:
            # 三字姓名：保留第一个字和最后一个字，中间用*替换
            masked_name = original_name[0] + '*' + original_name[2]
        else:
            # 四字及以上姓名：保留第一个字和最后一个字，中间全部用*替换
            masked_name = original_name[0] + '*' * (len(original_name) - 2) + original_name[-1]

        # 缓存映射
        self.name_mapping[original_name] = masked_name

        return masked_name

    def mask_name_consistent(self, original_name: str) -> str:
        """
        一致性脱敏姓名 - 相同姓名总是产生相同的脱敏结果
        使用哈希算法确保一致性，同时保护隐私

        Args:
            original_name: 原始姓名

        Returns:
            脱敏后的姓名（一致性）
        """
        if pd.isna(original_name) or not str(original_name).strip():
            return "匿名"

        original_name = str(original_name).strip()

        # 检查缓存
        if original_name in self.name_mapping:
            return self.name_mapping[original_name]

        # 生成一致性哈希
        hash_value = self._generate_consistent_hash(original_name)
        hash_int = int(hash_value[:8], 16)

        # 常见姓氏和名字列表
        surnames = ["王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
                   "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗"]

        given_names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋",
                      "勇", "艳", "杰", "涛", "明", "超", "秀", "英", "华", "慧"]

        # 基于哈希选择姓氏和名字
        surname_idx = hash_int % len(surnames)
        given_name_idx = (hash_int >> 8) % len(given_names)

        # 生成脱敏姓名
        masked_name = f"{surnames[surname_idx]}{given_names[given_name_idx]}"

        # 如果原始姓名是三个字，添加第二个名字
        if len(original_name) >= 3:
            second_name_idx = (hash_int >> 16) % len(given_names)
            masked_name += given_names[second_name_idx]

        # 缓存结果
        self.name_mapping[original_name] = masked_name
        return masked_name

    def mask_hospital_id(self, original_id: str) -> str:
        """
        掩码住院号：保留前4位和最后2位，中间用*替换
        例如：20250000001 → 2025*****01

        Args:
            original_id: 原始住院号/患者ID

        Returns:
            脱敏后的住院号
        """
        if pd.isna(original_id) or not str(original_id).strip():
            return original_id

        original_id = str(original_id).strip()

        # 如果已经映射过，直接返回
        if original_id in self.id_mapping:
            return self.id_mapping[original_id]

        # 住院号脱敏规则：保留前4位和最后2位，中间用*替换
        if len(original_id) <= 6:
            # 长度不足7位，保持原样或简单处理
            if len(original_id) <= 4:
                masked_id = original_id  # 太短，保持原样
            else:
                # 5-6位：保留前面部分，最后1-2位，中间用*
                if len(original_id) == 5:
                    masked_id = original_id[:3] + '*' + original_id[-1:]
                else:  # 长度为6
                    masked_id = original_id[:3] + '**' + original_id[-1:]
        else:
            # 长度7位及以上：保留前4位和最后2位，中间全部用*替换
            # 例如：20250000001 (11位) → 2025*****01
            middle_length = len(original_id) - 6  # 中间需要*的数量
            masked_id = original_id[:4] + '*' * middle_length + original_id[-2:]

        # 缓存映射
        self.id_mapping[original_id] = masked_id

        return masked_id

    def mask_hospital_id_consistent(self, original_id: str) -> str:
        """
        一致性脱敏住院号 - 相同ID总是产生相同的脱敏结果

        Args:
            original_id: 原始住院号/患者ID

        Returns:
            脱敏后的住院号（一致性）
        """
        if pd.isna(original_id) or not str(original_id).strip():
            return "UNKNOWN"

        original_id = str(original_id).strip()

        # 检查缓存
        if original_id in self.id_mapping:
            return self.id_mapping[original_id]

        # 生成一致性哈希
        hash_value = self._generate_consistent_hash(original_id)
        hash_int = int(hash_value[:16], 16)

        # 基于哈希生成脱敏ID
        if original_id.isdigit():
            # 纯数字ID：生成相同长度的数字
            masked_id = str(hash_int)[:len(original_id)].zfill(len(original_id))
        else:
            # 混合格式：保持字母数字结构
            masked_id = f"P{hash_int:010d}"[:len(original_id)]

        # 缓存结果
        self.id_mapping[original_id] = masked_id
        return masked_id

    def mask_phone_number(self, phone: str) -> str:
        """
        手机号脱敏
        
        Args:
            phone: 原始手机号
            
        Returns:
            脱敏后的手机号
        """
        if pd.isna(phone) or not str(phone).strip():
            return phone
            
        phone = str(phone).strip()
        
        # 手机号脱敏：保留前3位和后4位
        if len(phone) == 11 and phone.isdigit():
            return phone[:3] + '****' + phone[-4:]
        else:
            return phone

    def mask_phone_number_consistent(self, phone: str) -> str:
        """
        一致性脱敏手机号 - 相同手机号总是产生相同的脱敏结果

        Args:
            phone: 原始手机号

        Returns:
            脱敏后的手机号（一致性）
        """
        if pd.isna(phone) or not str(phone).strip():
            return "138****0000"

        phone = str(phone).strip()

        # 检查缓存
        if phone in self.phone_mapping:
            return self.phone_mapping[phone]

        # 生成一致性哈希
        hash_value = self._generate_consistent_hash(phone)
        hash_int = int(hash_value[:8], 16)

        # 生成脱敏手机号：138****后四位
        last_four = str(hash_int)[-4:].zfill(4)
        masked_phone = f"138****{last_four}"

        # 缓存结果
        self.phone_mapping[phone] = masked_phone
        return masked_phone

    def mask_id_card(self, id_card: str) -> str:
        """
        身份证号脱敏
        
        Args:
            id_card: 原始身份证号
            
        Returns:
            脱敏后的身份证号
        """
        if pd.isna(id_card) or not str(id_card).strip():
            return id_card
            
        id_card = str(id_card).strip()
        
        # 身份证号脱敏：保留前6位和后4位
        if len(id_card) == 18:
            return id_card[:6] + '********' + id_card[-4:]
        elif len(id_card) == 15:
            return id_card[:6] + '*****' + id_card[-4:]
        else:
            return id_card
    
    def anonymize_dataframe(self, df: pd.DataFrame, privacy_config: Dict[str, Any]) -> pd.DataFrame:
        """
        对DataFrame进行脱敏处理 - 安全模式：脱敏后删除原始数据

        Args:
            df: 原始数据
            privacy_config: 脱敏配置

        Returns:
            脱敏后的数据（不包含原始敏感信息）
        """
        logger.info("开始数据脱敏处理（安全模式）")

        # 创建副本避免修改原数据
        anonymized_df = df.copy()

        # 处理患者姓名 - 脱敏后删除原始数据
        if privacy_config.get('anonymize_names', True) and '患者姓名' in anonymized_df.columns:
            logger.info("脱敏处理：患者姓名")
            # 先脱敏
            anonymized_df['患者姓名'] = anonymized_df['患者姓名'].apply(self.mask_name)
            # 记录脱敏统计
            self._record_anonymization('患者姓名', len(anonymized_df))

        # 处理患者ID/住院号 - 脱敏后删除原始数据
        id_columns = ['患者ID', '住院号', '病案号']
        for col in id_columns:
            if privacy_config.get('anonymize_ids', True) and col in anonymized_df.columns:
                logger.info(f"脱敏处理：{col}")
                # 先脱敏
                anonymized_df[col] = anonymized_df[col].apply(self.mask_hospital_id)
                # 记录脱敏统计
                self._record_anonymization(col, len(anonymized_df))
        
        # 处理手机号（如果存在）
        if privacy_config.get('mask_phone', True):
            phone_columns = ['手机号', '联系电话', '电话号码']
            for col in phone_columns:
                if col in anonymized_df.columns:
                    logger.info(f"脱敏处理：{col}")
                    anonymized_df[col] = anonymized_df[col].apply(self.mask_phone_number)
        
        # 处理身份证号（如果存在）
        if privacy_config.get('mask_id_card', True):
            id_columns = ['身份证号', '身份证', 'ID卡号']
            for col in id_columns:
                if col in anonymized_df.columns:
                    logger.info(f"脱敏处理：{col}")
                    anonymized_df[col] = anonymized_df[col].apply(self.mask_id_card)
        
        # 处理医护人员姓名（默认启用医护人员姓名脱敏）
        staff_columns = [
            '麻醉医生', '主治医生', '手术医生', '医生姓名', '麻醉师', '主刀医生', '主麻', '副麻',
            '一助', '二助', '三助', '麻醉助手', '灌注医生',
            '洗手护士1', '洗手护士2', '巡回护士1', '巡回护士2', '接替器械护士', '接替巡回护士',
            'anesthesiologist', 'surgeon', 'assistant_anesthesiologist',
            'anesthesia_assistant', 'first_assistant', 'second_assistant',
            'third_assistant', 'perfusionist', 'scrub_nurse_1', 'scrub_nurse_2',
            'circulating_nurse_1', 'circulating_nurse_2', 'relief_scrub_nurse', 'relief_circulating_nurse'
        ]
        for col in staff_columns:
            if col in anonymized_df.columns:
                logger.info(f"脱敏处理：{col}")
                anonymized_df[col] = anonymized_df[col].apply(self.mask_name)
        
        # 移除敏感列（如果配置要求）
        sensitive_columns = privacy_config.get('remove_columns', [])
        for col in sensitive_columns:
            if col in anonymized_df.columns:
                logger.info(f"移除敏感列：{col}")
                anonymized_df.drop(col, axis=1, inplace=True)
        
        logger.info(f"数据脱敏处理完成（安全模式），处理了 {len(anonymized_df)} 条记录")
        logger.info("⚠️ 原始敏感数据已被永久删除，无法恢复")

        return anonymized_df

    def _record_anonymization(self, field_name: str, record_count: int):
        """记录脱敏统计"""
        self.anonymization_stats[field_name] = record_count

    def get_privacy_summary(self) -> Dict[str, Any]:
        """
        获取脱敏处理摘要 - 安全模式

        Returns:
            脱敏处理摘要信息（不包含原始数据映射）
        """
        return {
            'anonymized_names_count': len(self.name_mapping),
            'anonymized_ids_count': len(self.id_mapping),
            'field_stats': self.anonymization_stats.copy(),
            'security_mode': True,  # 标识为安全模式
            'original_data_deleted': True,  # 原始数据已删除
            'warning': '原始敏感数据已被永久删除，无法恢复'
        }


def get_default_privacy_config() -> Dict[str, Any]:
    """
    获取默认脱敏配置

    Returns:
        默认脱敏配置
    """
    return {
        'anonymize_names': True,        # 脱敏患者姓名（完全用*替换）
        'anonymize_ids': True,          # 脱敏患者ID/住院号（保留前4位和后2位）
        'mask_phone': True,             # 掩码手机号
        'mask_id_card': True,           # 掩码身份证号
        'anonymize_doctor_names': True,  # 脱敏医生姓名（默认启用）
        'remove_columns': [],           # 要移除的敏感列
        'keep_statistical_accuracy': True  # 保持统计准确性
    }
