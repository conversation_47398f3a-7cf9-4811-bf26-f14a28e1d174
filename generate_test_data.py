#!/usr/bin/env python3
"""
生成测试数据脚本 - 演示一致性脱敏
"""
import sys
import hashlib
import random
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from utils.logger import logger


class ConsistentDataMasker:
    """一致性数据脱敏器 - 确保相同输入产生相同输出"""
    
    def __init__(self, secret_key: str = "anesthesia_qc_2024"):
        """
        初始化脱敏器
        Args:
            secret_key: 用于生成一致性哈希的密钥
        """
        self.secret_key = secret_key
        self.name_cache = {}  # 姓名脱敏缓存
        self.id_cache = {}    # ID脱敏缓存
        
    def _generate_consistent_hash(self, data: str) -> str:
        """生成一致性哈希"""
        combined = f"{self.secret_key}:{data}"
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()
    
    def mask_name_consistent(self, original_name: str) -> str:
        """
        一致性姓名脱敏
        相同的姓名总是生成相同的脱敏结果
        """
        if not original_name or original_name.strip() == "":
            return "匿名"
            
        # 检查缓存
        if original_name in self.name_cache:
            return self.name_cache[original_name]
        
        # 生成一致性哈希
        hash_value = self._generate_consistent_hash(original_name)
        
        # 基于哈希生成脱敏姓名
        # 使用哈希的前几位来确定脱敏模式
        hash_int = int(hash_value[:8], 16)
        
        # 常见姓氏列表
        surnames = ["王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴", 
                   "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗"]
        
        # 常见名字
        given_names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋",
                      "勇", "艳", "杰", "涛", "明", "超", "秀", "英", "华", "慧"]
        
        # 基于哈希选择姓氏和名字
        surname_idx = hash_int % len(surnames)
        given_name_idx = (hash_int >> 8) % len(given_names)
        
        # 生成脱敏姓名
        masked_name = f"{surnames[surname_idx]}{given_names[given_name_idx]}"
        
        # 如果是三个字的姓名，添加第二个名字
        if len(original_name) >= 3:
            second_name_idx = (hash_int >> 16) % len(given_names)
            masked_name += given_names[second_name_idx]
        
        # 缓存结果
        self.name_cache[original_name] = masked_name
        return masked_name
    
    def mask_patient_id_consistent(self, original_id: str) -> str:
        """
        一致性患者ID脱敏
        相同的ID总是生成相同的脱敏结果
        """
        if not original_id or original_id.strip() == "":
            return "UNKNOWN"
            
        # 检查缓存
        if original_id in self.id_cache:
            return self.id_cache[original_id]
        
        # 生成一致性哈希
        hash_value = self._generate_consistent_hash(original_id)
        
        # 基于哈希生成脱敏ID
        # 保持原始ID的格式特征
        if original_id.isdigit():
            # 纯数字ID：生成相同长度的数字
            hash_int = int(hash_value[:16], 16)
            masked_id = str(hash_int)[:len(original_id)].zfill(len(original_id))
        else:
            # 混合格式：保持字母数字结构
            hash_int = int(hash_value[:16], 16)
            masked_id = f"P{hash_int:010d}"[:len(original_id)]
        
        # 缓存结果
        self.id_cache[original_id] = masked_id
        return masked_id
    
    def mask_phone_consistent(self, original_phone: str) -> str:
        """一致性电话号码脱敏"""
        if not original_phone or len(original_phone) < 7:
            return "138****0000"
        
        hash_value = self._generate_consistent_hash(original_phone)
        hash_int = int(hash_value[:8], 16)
        
        # 保持手机号格式：138****后四位
        last_four = str(hash_int)[-4:].zfill(4)
        return f"138****{last_four}"


def generate_test_patients(count: int = 100) -> pd.DataFrame:
    """生成测试患者数据"""
    
    # 真实姓名列表（用于生成原始数据）
    real_surnames = ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十",
                    "郑十一", "王小明", "李小红", "张小华", "刘小强", "陈小美",
                    "杨小军", "黄小丽", "赵小刚", "周小敏", "吴小燕", "徐小伟"]
    
    departments = ["心外科", "胸外科", "普外科", "骨科", "神经外科", "泌尿外科", 
                  "妇产科", "眼科", "耳鼻喉科", "口腔科"]
    
    anesthesia_methods = ["全身麻醉", "椎管内麻醉", "神经阻滞麻醉", "静脉麻醉", "局部麻醉"]
    
    surgery_types = ["阑尾切除术", "胆囊切除术", "疝修补术", "甲状腺切除术", 
                    "骨折内固定术", "关节置换术", "白内障手术", "扁桃体切除术"]
    
    patients = []
    
    # 生成一些重复患者（用于测试一致性脱敏）
    duplicate_patients = random.sample(real_surnames, min(10, len(real_surnames)))
    
    for i in range(count):
        # 30%概率生成重复患者
        if i < len(duplicate_patients) * 3 and random.random() < 0.3:
            name = duplicate_patients[i % len(duplicate_patients)]
            patient_id = f"P{1000 + (i % len(duplicate_patients)):04d}"
        else:
            name = random.choice(real_surnames)
            patient_id = f"P{1000 + i:04d}"
        
        # 生成患者数据
        patient = {
            'patient_id': patient_id,
            'patient_name': name,
            'age': random.randint(18, 85),
            'gender': random.choice(['男', '女']),
            'phone': f"138{random.randint(10000000, 99999999)}",
            'surgery_date': (datetime.now() - timedelta(days=random.randint(0, 365))).strftime('%Y-%m-%d'),
            'department': random.choice(departments),
            'anesthesia_method': random.choice(anesthesia_methods),
            'surgery_type': random.choice(surgery_types),
            'duration_minutes': random.randint(30, 480),
            'anesthesiologist': f"医生{random.randint(1, 20)}",
            'surgeon': f"主刀{random.randint(1, 15)}"
        }
        
        patients.append(patient)
    
    return pd.DataFrame(patients)


def demonstrate_consistent_masking():
    """演示一致性脱敏"""
    print("🔒 一致性脱敏演示")
    print("=" * 60)
    
    # 创建脱敏器
    masker = ConsistentDataMasker()
    
    # 测试数据
    test_names = ["张三", "李四", "张三", "王五", "李四"]  # 包含重复
    test_ids = ["P1001", "P1002", "P1001", "P1003", "P1002"]  # 包含重复
    
    print("📋 姓名脱敏测试：")
    print("原始姓名 -> 脱敏姓名")
    print("-" * 30)
    for name in test_names:
        masked = masker.mask_name_consistent(name)
        print(f"{name:8} -> {masked}")
    
    print("\n📋 患者ID脱敏测试：")
    print("原始ID   -> 脱敏ID")
    print("-" * 30)
    for pid in test_ids:
        masked = masker.mask_patient_id_consistent(pid)
        print(f"{pid:8} -> {masked}")
    
    print("\n✅ 一致性验证：")
    print("- 相同的原始数据总是产生相同的脱敏结果")
    print("- 不同的原始数据产生不同的脱敏结果")
    print("- 脱敏结果无法逆向推导出原始数据")


def generate_excel_test_data():
    """生成Excel测试数据文件"""
    print("\n📊 生成Excel测试数据...")
    
    # 生成测试数据
    df = generate_test_patients(200)
    
    # 保存原始数据
    original_file = "test_data_original.xlsx"
    df.to_excel(original_file, index=False, sheet_name="原始数据")
    print(f"✅ 原始测试数据已保存到: {original_file}")
    
    # 创建脱敏器并进行脱敏
    masker = ConsistentDataMasker()
    
    # 脱敏处理
    df_masked = df.copy()
    df_masked['patient_name'] = df_masked['patient_name'].apply(masker.mask_name_consistent)
    df_masked['patient_id'] = df_masked['patient_id'].apply(masker.mask_patient_id_consistent)
    df_masked['phone'] = df_masked['phone'].apply(masker.mask_phone_consistent)
    
    # 保存脱敏数据
    masked_file = "test_data_masked.xlsx"
    df_masked.to_excel(masked_file, index=False, sheet_name="脱敏数据")
    print(f"✅ 脱敏测试数据已保存到: {masked_file}")
    
    # 生成对比报告
    print("\n📈 脱敏统计报告：")
    print(f"- 总记录数: {len(df)}")
    print(f"- 唯一原始姓名: {df['patient_name'].nunique()}")
    print(f"- 唯一脱敏姓名: {df_masked['patient_name'].nunique()}")
    print(f"- 唯一原始ID: {df['patient_id'].nunique()}")
    print(f"- 唯一脱敏ID: {df_masked['patient_id'].nunique()}")
    
    # 验证一致性
    print("\n🔍 一致性验证：")
    
    # 检查重复姓名的脱敏一致性
    name_groups = df.groupby('patient_name')
    for name, group in name_groups:
        if len(group) > 1:
            masked_names = df_masked[df_masked.index.isin(group.index)]['patient_name'].unique()
            if len(masked_names) == 1:
                print(f"✅ 姓名 '{name}' 的 {len(group)} 条记录都脱敏为 '{masked_names[0]}'")
            else:
                print(f"❌ 姓名 '{name}' 脱敏不一致: {masked_names}")
    
    return original_file, masked_file


def main():
    """主函数"""
    print("🧪 测试数据生成器 - 一致性脱敏演示")
    print("=" * 60)
    
    try:
        # 演示一致性脱敏
        demonstrate_consistent_masking()
        
        # 生成Excel测试数据
        original_file, masked_file = generate_excel_test_data()
        
        print("\n" + "=" * 60)
        print("✅ 测试数据生成完成！")
        print("\n📁 生成的文件：")
        print(f"1. {original_file} - 原始测试数据")
        print(f"2. {masked_file} - 脱敏测试数据")
        
        print("\n💡 使用建议：")
        print("1. 可以将脱敏数据导入系统进行测试")
        print("2. 对比原始数据和脱敏数据验证一致性")
        print("3. 多次运行脚本验证相同输入产生相同输出")
        
        print("\n🔒 安全特性：")
        print("- ✅ 相同原始数据 → 相同脱敏结果")
        print("- ✅ 不同原始数据 → 不同脱敏结果") 
        print("- ✅ 脱敏结果不可逆向推导")
        print("- ✅ 保持数据关联性和一致性")
        
    except Exception as e:
        logger.error(f"生成测试数据失败: {str(e)}")
        print(f"❌ 错误: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
