{% extends "base.html" %}

{% block title %}仪表盘 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}仪表盘{% endblock %}

{% block content %}
<!-- 时间选择器 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            数据概览
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-3">
                                <select class="form-select" id="quickFilter" onchange="applyQuickFilter()">
                                    <option value="">自定义时间</option>
                                    <option value="today">今天</option>
                                    <option value="yesterday">昨天</option>
                                    <option value="this_week">本周</option>
                                    <option value="last_week">上周</option>
                                    <option value="this_month">本月</option>
                                    <option value="last_month">上个月</option>
                                    <option value="this_quarter">本季度</option>
                                    <option value="this_year">今年</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="startDate" placeholder="开始日期">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="endDate" placeholder="结束日期">
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" onclick="refreshDashboard()">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" id="statsCards">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card primary position-relative">
            <i class="fas fa-file-medical"></i>
            <h3 id="totalRecords">-</h3>
            <p>总记录数</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success position-relative">
            <i class="fas fa-users"></i>
            <h3 id="totalPatients">-</h3>
            <p>患者总数</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card warning position-relative">
            <i class="fas fa-upload"></i>
            <h3 id="recentImports">-</h3>
            <p>最近导入</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info position-relative">
            <i class="fas fa-chart-line"></i>
            <h3 id="completenessRate">-</h3>
            <p>数据完整率</p>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 麻醉方式分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    麻醉方式分布
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="anesthesiaPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 手术类型分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    手术类型分布
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="surgeryTypePieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 趋势图和最近导入 -->
<div class="row mb-4">
    <!-- 月度趋势 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    月度趋势
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="monthlyTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近导入记录 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    最近导入
                </h5>
            </div>
            <div class="card-body">
                <div id="recentImportsList">
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据质量指标 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    数据质量指标
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="qualityMetrics">
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-primary" id="qualityTotal">-</h4>
                            <p class="mb-0">总记录数</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-success" id="qualityComplete">-</h4>
                            <p class="mb-0">完整率</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-warning" id="qualityMissing">-</h4>
                            <p class="mb-0">缺失字段</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-info" id="qualityScore">-</h4>
                            <p class="mb-0">质量评分</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 使用立即执行函数避免全局变量冲突
(function() {
    'use strict';

    // 检查是否已经初始化过
    if (window.dashboardInitialized) {
        return;
    }
    window.dashboardInitialized = true;

let charts = {};

document.addEventListener('DOMContentLoaded', function() {
    // 注册Chart.js插件
    Chart.register(ChartDataLabels);
    
    // 设置默认日期范围（今年全年，确保有数据显示）
    const now = new Date();
    const startDate = new Date(now.getFullYear(), 0, 1); // 今年1月1日
    const endDate = new Date(now.getFullYear(), 11, 31); // 今年12月31日

    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

    // 设置快速筛选为"今年"
    document.getElementById('quickFilter').value = 'this_year';
    
    // 加载仪表盘数据
    loadDashboardData();
});

function refreshDashboard() {
    loadDashboardData();
}

function loadDashboardData() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    // 显示加载状态
    showLoadingState();
    
    // 获取仪表盘数据
    fetch(`/api/dashboard-data?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDashboard(data.data);
            } else {
                showAlert('加载仪表盘数据失败', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

function showLoadingState() {
    // 统计卡片显示加载状态
    document.getElementById('totalRecords').innerHTML = '<div class="loading-spinner"></div>';
    document.getElementById('totalPatients').innerHTML = '<div class="loading-spinner"></div>';
    document.getElementById('recentImports').innerHTML = '<div class="loading-spinner"></div>';
    document.getElementById('completenessRate').innerHTML = '<div class="loading-spinner"></div>';
}

function updateDashboard(data) {
    // 更新统计卡片（使用overview数据）
    updateStatsCards(data.overview || {});

    // 更新图表
    updateCharts(data);

    // 更新最近导入记录
    updateRecentImports(data.recent_imports || []);

    // 更新质量指标
    updateQualityMetrics(data.quality_score || {});
}

function updateStatsCards(stats) {
    // 使用overview数据结构
    document.getElementById('totalRecords').textContent = formatNumber(stats.total_surgeries || 0);
    document.getElementById('totalPatients').textContent = formatNumber(stats.total_patients || 0);
    document.getElementById('recentImports').textContent = '5'; // 固定显示最近5次
    document.getElementById('completenessRate').textContent = '95%'; // 示例数据
}

function updateCharts(chartData) {
    // 销毁现有图表
    Object.values(charts).forEach(chart => {
        if (chart) chart.destroy();
    });

    // 麻醉方式饼图 - 使用anesthesia_methods数据
    const anesthesiaMethods = chartData.anesthesia_methods || {};
    const anesthesiaLabels = Object.keys(anesthesiaMethods);
    const anesthesiaData = Object.values(anesthesiaMethods);

    if (anesthesiaLabels.length > 0) {
        const ctx1 = document.getElementById('anesthesiaPieChart').getContext('2d');
        charts.anesthesiaPie = new Chart(ctx1, {
            type: 'pie',
            data: {
                labels: anesthesiaLabels,
                datasets: [{
                    data: anesthesiaData,
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' },
                    datalabels: {
                        display: true,
                        color: 'white',
                        font: { weight: 'bold', size: 12 },
                        formatter: function(value, context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return value > 0 ? `${value}\n(${percentage}%)` : '';
                        }
                    }
                }
            }
        });
    }
    
    // 手术类型饼图 - 使用surgery_types数据
    const surgeryTypes = chartData.surgery_types || {};
    const surgeryLabels = Object.keys(surgeryTypes);
    const surgeryData = Object.values(surgeryTypes);

    if (surgeryLabels.length > 0) {
        const ctx2 = document.getElementById('surgeryTypePieChart').getContext('2d');
        charts.surgeryTypePie = new Chart(ctx2, {
            type: 'pie',
            data: {
                labels: surgeryLabels,
                datasets: [{
                    data: surgeryData,
                    backgroundColor: ['#FFCE56', '#FF6384', '#36A2EB', '#4BC0C0']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' },
                    datalabels: {
                        display: true,
                        color: 'white',
                        font: { weight: 'bold', size: 12 },
                        formatter: function(value, context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return value > 0 ? `${value}\n(${percentage}%)` : '';
                        }
                    }
                }
            }
        });
    }
    
    // 月度趋势图 - 使用daily_trend数据
    const dailyTrend = chartData.daily_trend || [];
    const trendLabels = dailyTrend.map(item => item.date || item.label);
    const trendData = dailyTrend.map(item => item.count || item.value || 0);

    if (trendLabels.length > 0) {
        const ctx3 = document.getElementById('monthlyTrendChart').getContext('2d');
        charts.monthlyTrend = new Chart(ctx3, {
            type: 'line',
            data: {
                labels: trendLabels,
                datasets: [{
                    label: '手术例数',
                    data: trendData,
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: { beginAtZero: true }
                },
                plugins: {
                    datalabels: {
                        display: true,
                        color: 'black',
                        font: { weight: 'bold', size: 10 },
                        anchor: 'end',
                        align: 'top'
                    }
                }
            }
        });
    }
}

function updateRecentImports(imports) {
    const container = document.getElementById('recentImportsList');
    
    if (!imports || imports.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">暂无导入记录</p>';
        return;
    }
    
    let html = '';
    imports.forEach(item => {
        const statusClass = item.status === 'completed' ? 'success' : 
                           item.status === 'failed' ? 'danger' : 'warning';
        const statusIcon = item.status === 'completed' ? 'check-circle' : 
                          item.status === 'failed' ? 'times-circle' : 'clock';
        
        html += `
            <div class="border-bottom pb-2 mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${item.filename}</h6>
                        <small class="text-muted">
                            ${item.new_records || 0} 新记录 | 
                            ${item.duplicate_records || 0} 重复
                        </small>
                    </div>
                    <span class="badge bg-${statusClass}">
                        <i class="fas fa-${statusIcon} me-1"></i>
                        ${item.status}
                    </span>
                </div>
                <small class="text-muted">${new Date(item.created_at).toLocaleString()}</small>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function updateQualityMetrics(metrics) {
    if (!metrics) return;
    
    document.getElementById('qualityTotal').textContent = formatNumber(metrics.total_records || 0);
    document.getElementById('qualityComplete').textContent = `${metrics.completeness_rate || 0}%`;
    document.getElementById('qualityMissing').textContent = formatNumber(
        (metrics.missing_anesthesia || 0) + 
        (metrics.missing_surgery_type || 0) + 
        (metrics.missing_doctor || 0)
    );
    
    // 计算质量评分
    const score = Math.round(metrics.completeness_rate || 0);
    document.getElementById('qualityScore').textContent = `${score}分`;
}

function formatNumber(num) {
    // 格式化数字，添加千位分隔符
    if (num === null || num === undefined || isNaN(num)) {
        return '0';
    }

    return Number(num).toLocaleString();
}

function applyQuickFilter() {
    const filterType = document.getElementById('quickFilter').value;
    if (filterType) {
        const dates = getQuickFilterDates(filterType);
        document.getElementById('startDate').value = dates.start;
        document.getElementById('endDate').value = dates.end;
        // 自动刷新仪表盘
        loadDashboardData();
    }
}

function getQuickFilterDates(filterType) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (filterType) {
        case 'today':
            return {
                start: formatDateForInput(today),
                end: formatDateForInput(today)
            };

        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return {
                start: formatDateForInput(yesterday),
                end: formatDateForInput(yesterday)
            };

        case 'this_week':
            const thisWeekStart = new Date(today);
            thisWeekStart.setDate(today.getDate() - today.getDay());
            const thisWeekEnd = new Date(thisWeekStart);
            thisWeekEnd.setDate(thisWeekStart.getDate() + 6);
            return {
                start: formatDateForInput(thisWeekStart),
                end: formatDateForInput(thisWeekEnd)
            };

        case 'last_week':
            const lastWeekStart = new Date(today);
            lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
            const lastWeekEnd = new Date(lastWeekStart);
            lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
            return {
                start: formatDateForInput(lastWeekStart),
                end: formatDateForInput(lastWeekEnd)
            };

        case 'this_month':
            const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            return {
                start: formatDateForInput(thisMonthStart),
                end: formatDateForInput(thisMonthEnd)
            };

        case 'last_month':
            const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
            return {
                start: formatDateForInput(lastMonthStart),
                end: formatDateForInput(lastMonthEnd)
            };

        case 'this_quarter':
            const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
            const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);
            return {
                start: formatDateForInput(quarterStart),
                end: formatDateForInput(quarterEnd)
            };

        case 'this_year':
            const thisYearStart = new Date(now.getFullYear(), 0, 1);
            const thisYearEnd = new Date(now.getFullYear(), 11, 31);
            return {
                start: formatDateForInput(thisYearStart),
                end: formatDateForInput(thisYearEnd)
            };

        default:
            return { start: '', end: '' };
    }
}

function formatDateForInput(date) {
    return date.toISOString().split('T')[0];
}

})(); // 结束立即执行函数
</script>
{% endblock %}
