/**
 * 资源加载器 - 优化页面加载性能
 */

class ResourceLoader {
    constructor() {
        this.loadedResources = new Set();
        this.loadingPromises = new Map();
    }

    /**
     * 异步加载CSS文件
     */
    loadCSS(href, id = null) {
        if (this.loadedResources.has(href)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(href)) {
            return this.loadingPromises.get(href);
        }

        const promise = new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            if (id) link.id = id;
            
            link.onload = () => {
                this.loadedResources.add(href);
                resolve();
            };
            
            link.onerror = () => {
                reject(new Error(`CSS加载失败: ${href}`));
            };
            
            document.head.appendChild(link);
        });

        this.loadingPromises.set(href, promise);
        return promise;
    }

    /**
     * 异步加载JavaScript文件
     */
    loadJS(src, id = null) {
        if (this.loadedResources.has(src)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(src)) {
            return this.loadingPromises.get(src);
        }

        const promise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            if (id) script.id = id;
            
            script.onload = () => {
                this.loadedResources.add(src);
                resolve();
            };
            
            script.onerror = () => {
                reject(new Error(`JS加载失败: ${src}`));
            };
            
            document.head.appendChild(script);
        });

        this.loadingPromises.set(src, promise);
        return promise;
    }

    /**
     * 预加载资源
     */
    preload(href, as = 'script') {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = href;
        link.as = as;
        document.head.appendChild(link);
    }

    /**
     * 批量加载资源
     */
    async loadBatch(resources) {
        const promises = resources.map(resource => {
            if (resource.type === 'css') {
                return this.loadCSS(resource.src, resource.id);
            } else if (resource.type === 'js') {
                return this.loadJS(resource.src, resource.id);
            }
        });

        try {
            await Promise.all(promises);
            console.log('✅ 批量资源加载完成');
        } catch (error) {
            console.error('❌ 批量资源加载失败:', error);
            throw error;
        }
    }

    /**
     * 检查资源是否已加载
     */
    isLoaded(src) {
        return this.loadedResources.has(src);
    }

    /**
     * 清理加载状态
     */
    clear() {
        this.loadedResources.clear();
        this.loadingPromises.clear();
    }
}

// 全局资源加载器实例
window.ResourceLoader = new ResourceLoader();

/**
 * 页面性能监控
 */
class PerformanceMonitor {
    constructor() {
        this.startTime = performance.now();
        this.metrics = {};
    }

    /**
     * 记录性能指标
     */
    mark(name) {
        this.metrics[name] = performance.now() - this.startTime;
        console.log(`⏱️ ${name}: ${this.metrics[name].toFixed(2)}ms`);
    }

    /**
     * 获取页面加载性能
     */
    getPageLoadMetrics() {
        if (performance.getEntriesByType) {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                return {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalTime: navigation.loadEventEnd - navigation.fetchStart
                };
            }
        }
        return null;
    }

    /**
     * 输出性能报告
     */
    report() {
        console.group('📊 页面性能报告');
        
        // 自定义指标
        Object.entries(this.metrics).forEach(([name, time]) => {
            console.log(`${name}: ${time.toFixed(2)}ms`);
        });

        // 浏览器性能指标
        const pageMetrics = this.getPageLoadMetrics();
        if (pageMetrics) {
            console.log(`DOM加载: ${pageMetrics.domContentLoaded.toFixed(2)}ms`);
            console.log(`页面加载: ${pageMetrics.loadComplete.toFixed(2)}ms`);
            console.log(`总时间: ${pageMetrics.totalTime.toFixed(2)}ms`);
        }

        console.groupEnd();
    }
}

// 全局性能监控实例
window.PerformanceMonitor = new PerformanceMonitor();

/**
 * 懒加载图片
 */
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * 初始化性能优化
 */
document.addEventListener('DOMContentLoaded', function() {
    window.PerformanceMonitor.mark('DOM Ready');
    
    // 初始化懒加载
    initLazyLoading();
    
    // 预加载关键资源
    const criticalResources = [
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'
    ];
    
    criticalResources.forEach(resource => {
        window.ResourceLoader.preload(resource);
    });
});

window.addEventListener('load', function() {
    window.PerformanceMonitor.mark('Page Load Complete');
    
    // 延迟输出性能报告
    setTimeout(() => {
        window.PerformanceMonitor.report();
    }, 100);
});
