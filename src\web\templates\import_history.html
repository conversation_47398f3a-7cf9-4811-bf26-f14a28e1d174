{% extends "base.html" %}

{% block title %}导入历史 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}导入历史{% endblock %}

{% block content %}
<!-- 筛选和搜索 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">状态筛选</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="completed">已完成</option>
                            <option value="processing">处理中</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="dateFilter" class="form-label">时间范围</label>
                        <select class="form-select" id="dateFilter">
                            <option value="7">最近7天</option>
                            <option value="30" selected>最近30天</option>
                            <option value="90">最近90天</option>
                            <option value="all">全部</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="searchKeyword" class="form-label">搜索文件名</label>
                        <input type="text" class="form-control" id="searchKeyword" placeholder="输入文件名搜索...">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button class="btn btn-primary w-100" onclick="refreshHistory()">
                            <i class="fas fa-search me-1"></i>
                            搜索
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card primary position-relative">
            <i class="fas fa-upload"></i>
            <h3 id="totalImports">-</h3>
            <p>总导入次数</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success position-relative">
            <i class="fas fa-check-circle"></i>
            <h3 id="successfulImports">-</h3>
            <p>成功导入</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card danger position-relative">
            <i class="fas fa-times-circle"></i>
            <h3 id="failedImports">-</h3>
            <p>失败导入</p>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info position-relative">
            <i class="fas fa-database"></i>
            <h3 id="totalRecords">-</h3>
            <p>总导入记录</p>
        </div>
    </div>
</div>

<!-- 导入历史列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    导入记录
                </h5>
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-danger btn-sm me-2" onclick="clearHistory()">
                        <i class="fas fa-trash me-1"></i>
                        清理历史
                    </button>
                    <button class="btn btn-success btn-sm" onclick="exportHistory()">
                        <i class="fas fa-download me-1"></i>
                        导出历史
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="loadingIndicator" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载导入历史...</p>
                </div>
                
                <div id="historyList" style="display: none;">
                    <!-- 历史记录将在这里动态生成 -->
                </div>
                
                <div id="noDataMessage" class="text-center py-4" style="display: none;">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无导入记录</h5>
                    <p class="text-muted">
                        还没有导入过数据，
                        <a href="/data-import" class="text-decoration-none">立即导入</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="importDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    导入详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="importDetailContent">
                <!-- 详情内容将在这里动态生成 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="reprocessImport()">
                    <i class="fas fa-redo me-1"></i>
                    重新处理
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 使用立即执行函数避免全局变量冲突
(function() {
    'use strict';

    // 检查是否已经初始化过
    if (window.importHistoryInitialized) {
        console.log('导入历史页面已初始化，跳过重复初始化');
        return;
    }
    window.importHistoryInitialized = true;

    // 使用局部变量，避免全局冲突
    let currentImportId = null;

    document.addEventListener('DOMContentLoaded', function() {
    // 绑定事件
    document.getElementById('statusFilter').addEventListener('change', refreshHistory);
    document.getElementById('dateFilter').addEventListener('change', refreshHistory);
    document.getElementById('searchKeyword').addEventListener('input', debounce(refreshHistory, 500));
    
    // 初始加载
    loadImportHistory();
});

function refreshHistory() {
    loadImportHistory();
}

function loadImportHistory() {
    showLoading();
    
    // 获取筛选条件
    const status = document.getElementById('statusFilter').value;
    const dateRange = document.getElementById('dateFilter').value;
    const keyword = document.getElementById('searchKeyword').value;
    
    // 模拟API调用
    setTimeout(() => {
        const mockData = generateMockHistory(status, dateRange, keyword);
        updateHistoryList(mockData.records);
        updateStatistics(mockData.statistics);
        hideLoading();
    }, 1000);
}

function generateMockHistory(status, dateRange, keyword) {
    const records = [];
    const statuses = ['completed', 'failed', 'processing'];
    const filenames = [
        '麻醉记录_2025_01.xlsx',
        '手术数据_2025_02.xlsx',
        '质控数据_2025_03.xlsx',
        '麻醉统计_2025_04.xlsx',
        '手术记录_2025_05.xlsx'
    ];
    
    for (let i = 1; i <= 20; i++) {
        const recordStatus = statuses[Math.floor(Math.random() * statuses.length)];
        const filename = filenames[Math.floor(Math.random() * filenames.length)];
        
        // 应用筛选
        if (status && recordStatus !== status) continue;
        if (keyword && !filename.toLowerCase().includes(keyword.toLowerCase())) continue;
        
        const record = {
            id: `batch_${i}`,
            filename: filename,
            status: recordStatus,
            total_records: Math.floor(Math.random() * 1000) + 100,
            new_records: Math.floor(Math.random() * 800) + 50,
            duplicate_records: Math.floor(Math.random() * 200),
            error_records: Math.floor(Math.random() * 10),
            privacy_enabled: Math.random() > 0.5,
            created_at: new Date(2025, 0, Math.floor(Math.random() * 30) + 1).toISOString(),
            completed_at: recordStatus === 'completed' ? new Date(2025, 0, Math.floor(Math.random() * 30) + 1).toISOString() : null,
            error_message: recordStatus === 'failed' ? '文件格式错误' : null
        };
        
        records.push(record);
    }
    
    const statistics = {
        total: records.length,
        successful: records.filter(r => r.status === 'completed').length,
        failed: records.filter(r => r.status === 'failed').length,
        totalRecords: records.reduce((sum, r) => sum + r.new_records, 0)
    };
    
    return { records, statistics };
}

function updateHistoryList(records) {
    const container = document.getElementById('historyList');
    
    if (records.length === 0) {
        document.getElementById('historyList').style.display = 'none';
        document.getElementById('noDataMessage').style.display = 'block';
        return;
    }
    
    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('historyList').style.display = 'block';
    
    let html = '';
    
    records.forEach(record => {
        const statusClass = getStatusClass(record.status);
        const statusIcon = getStatusIcon(record.status);
        const statusText = getStatusText(record.status);
        
        html += `
            <div class="border rounded p-3 mb-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-excel fa-2x text-success me-3"></i>
                            <div>
                                <h6 class="mb-1">${record.filename}</h6>
                                <small class="text-muted">
                                    批次ID: ${record.id}
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <span class="badge bg-${statusClass} fs-6">
                                <i class="fas fa-${statusIcon} me-1"></i>
                                ${statusText}
                            </span>
                            ${record.privacy_enabled ? '<br><small class="text-muted"><i class="fas fa-shield-alt me-1"></i>已脱敏</small>' : ''}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-end">
                            <button class="btn btn-outline-primary btn-sm me-1" onclick="viewImportDetail('${record.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${record.status === 'failed' ? `
                                <button class="btn btn-outline-warning btn-sm me-1" onclick="retryImport('${record.id}')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteImport('${record.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-12">
                        <div class="row text-center">
                            <div class="col-3">
                                <small class="text-muted">总记录</small>
                                <div class="fw-bold">${formatNumber(record.total_records)}</div>
                            </div>
                            <div class="col-3">
                                <small class="text-muted">新增</small>
                                <div class="fw-bold text-success">${formatNumber(record.new_records)}</div>
                            </div>
                            <div class="col-3">
                                <small class="text-muted">重复</small>
                                <div class="fw-bold text-warning">${formatNumber(record.duplicate_records)}</div>
                            </div>
                            <div class="col-3">
                                <small class="text-muted">错误</small>
                                <div class="fw-bold text-danger">${formatNumber(record.error_records)}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-12">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            导入时间: ${new Date(record.created_at).toLocaleString()}
                            ${record.completed_at ? ` | 完成时间: ${new Date(record.completed_at).toLocaleString()}` : ''}
                        </small>
                        ${record.error_message ? `<br><small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>${record.error_message}</small>` : ''}
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function updateStatistics(stats) {
    document.getElementById('totalImports').textContent = formatNumber(stats.total);
    document.getElementById('successfulImports').textContent = formatNumber(stats.successful);
    document.getElementById('failedImports').textContent = formatNumber(stats.failed);
    document.getElementById('totalRecords').textContent = formatNumber(stats.totalRecords);
}

function getStatusClass(status) {
    switch (status) {
        case 'completed': return 'success';
        case 'failed': return 'danger';
        case 'processing': return 'warning';
        default: return 'secondary';
    }
}

function getStatusIcon(status) {
    switch (status) {
        case 'completed': return 'check-circle';
        case 'failed': return 'times-circle';
        case 'processing': return 'clock';
        default: return 'question-circle';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'completed': return '已完成';
        case 'failed': return '失败';
        case 'processing': return '处理中';
        default: return '未知';
    }
}

function viewImportDetail(importId) {
    currentImportId = importId;
    
    // 模拟获取详情数据
    const mockDetail = {
        id: importId,
        filename: '麻醉记录_2025_01.xlsx',
        status: 'completed',
        total_records: 856,
        new_records: 720,
        duplicate_records: 136,
        error_records: 0,
        privacy_enabled: true,
        created_at: '2025-01-15 10:30:00',
        completed_at: '2025-01-15 10:32:15',
        processing_time: '2分15秒',
        file_size: '2.5 MB',
        privacy_summary: {
            anonymized_names_count: 720,
            anonymized_ids_count: 720
        }
    };
    
    const content = document.getElementById('importDetailContent');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>文件名:</strong></td><td>${mockDetail.filename}</td></tr>
                    <tr><td><strong>批次ID:</strong></td><td>${mockDetail.id}</td></tr>
                    <tr><td><strong>文件大小:</strong></td><td>${mockDetail.file_size}</td></tr>
                    <tr><td><strong>处理时长:</strong></td><td>${mockDetail.processing_time}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>处理结果</h6>
                <table class="table table-sm">
                    <tr><td><strong>总记录数:</strong></td><td>${formatNumber(mockDetail.total_records)}</td></tr>
                    <tr><td><strong>新增记录:</strong></td><td class="text-success">${formatNumber(mockDetail.new_records)}</td></tr>
                    <tr><td><strong>重复记录:</strong></td><td class="text-warning">${formatNumber(mockDetail.duplicate_records)}</td></tr>
                    <tr><td><strong>错误记录:</strong></td><td class="text-danger">${formatNumber(mockDetail.error_records)}</td></tr>
                </table>
            </div>
        </div>
        
        ${mockDetail.privacy_enabled ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6><i class="fas fa-shield-alt me-2"></i>数据脱敏信息</h6>
                    <div class="alert alert-info">
                        <p class="mb-0">
                            <strong>脱敏状态:</strong> 已启用 |
                            <strong>脱敏姓名:</strong> ${mockDetail.privacy_summary.anonymized_names_count} 个 |
                            <strong>脱敏ID:</strong> ${mockDetail.privacy_summary.anonymized_ids_count} 个
                        </p>
                    </div>
                </div>
            </div>
        ` : ''}
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>时间线</h6>
                <div class="timeline">
                    <div class="timeline-item">
                        <i class="fas fa-upload text-primary"></i>
                        <span>文件上传完成 - ${mockDetail.created_at}</span>
                    </div>
                    <div class="timeline-item">
                        <i class="fas fa-cog text-warning"></i>
                        <span>开始数据处理 - ${mockDetail.created_at}</span>
                    </div>
                    <div class="timeline-item">
                        <i class="fas fa-check text-success"></i>
                        <span>处理完成 - ${mockDetail.completed_at}</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('importDetailModal')).show();
}

function retryImport(importId) {
    if (confirm('确定要重新处理这个导入吗？')) {
        showAlert('正在重新处理导入...', 'info');
        setTimeout(() => {
            showAlert('重新处理完成！', 'success');
            refreshHistory();
        }, 2000);
    }
}

function deleteImport(importId) {
    if (confirm('确定要删除这个导入记录吗？此操作不可恢复。')) {
        showAlert('正在删除导入记录...', 'info');
        setTimeout(() => {
            showAlert('导入记录已删除！', 'success');
            refreshHistory();
        }, 1000);
    }
}

function clearHistory() {
    if (confirm('确定要清理所有历史记录吗？此操作不可恢复。')) {
        showAlert('正在清理历史记录...', 'info');
        setTimeout(() => {
            showAlert('历史记录清理完成！', 'success');
            refreshHistory();
        }, 2000);
    }
}

function exportHistory() {
    showAlert('正在导出历史记录...', 'info');
    setTimeout(() => {
        showAlert('历史记录导出完成！', 'success');
    }, 2000);
}

function reprocessImport() {
    if (currentImportId) {
        retryImport(currentImportId);
        bootstrap.Modal.getInstance(document.getElementById('importDetailModal')).hide();
    }
}

function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('historyList').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

})(); // 结束立即执行函数
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    padding-bottom: 10px;
}

.timeline-item i {
    position: absolute;
    left: -30px;
    top: 2px;
    width: 20px;
    text-align: center;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 20px;
    width: 2px;
    height: calc(100% - 10px);
    background-color: #dee2e6;
}
</style>
{% endblock %}
