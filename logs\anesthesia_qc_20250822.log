2025-08-22 07:59:23,470 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 07:59:23,480 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,466 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,466 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 07:59:24,466 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 07:59:24,466 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 07:59:24,466 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 07:59:24,466 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,466 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,472 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,472 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,472 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,472 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,472 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,478 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,479 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,480 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,483 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,484 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,487 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,491 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 07:59:24,491 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 07:59:24,491 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 07:59:24,744 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 07:59:24,744 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,988 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,988 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 07:59:24,989 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 07:59:24,989 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 07:59:24,989 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 07:59:24,990 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,991 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,992 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,992 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,993 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,993 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,995 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,996 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,997 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:24,999 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:25,000 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:25,002 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:25,003 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 07:59:25,005 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 07:59:25,005 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 07:59:25,005 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 07:59:38,317 - anesthesia_qc - ERROR - 获取手术记录列表失败: no such column: p.name
2025-08-22 08:00:50,290 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:00:50,290 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,505 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,515 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,516 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,517 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,518 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,519 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,522 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,524 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,525 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,526 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:00:50,527 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:00:50,528 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:00:50,528 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:01:03,200 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:01:03,211 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,417 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,417 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:01:03,417 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:01:03,417 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:01:03,417 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:01:03,417 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,427 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,427 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,427 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,427 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,427 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,431 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,432 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,433 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,437 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,438 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,439 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,440 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:03,442 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:01:03,442 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:01:03,442 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:01:17,112 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:01:17,123 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,335 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,342 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,344 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,345 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,345 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,349 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,350 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,352 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,353 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:17,354 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:01:17,354 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:01:17,354 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:01:38,282 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:01:38,282 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,510 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,520 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,526 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,527 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,528 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,529 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,530 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,534 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,535 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,536 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:38,538 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:01:38,538 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:01:38,539 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:01:54,338 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:01:54,338 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,556 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,565 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,566 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,567 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,568 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,569 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,572 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,574 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,575 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:01:54,576 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:01:54,576 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:01:54,577 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:02:13,722 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:02:13,732 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,950 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,950 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:02:13,950 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:02:13,950 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:02:13,950 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:02:13,950 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,950 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,960 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,960 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,960 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,960 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,960 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,960 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,966 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,968 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,970 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,970 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,973 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:13,975 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:02:13,976 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:02:13,976 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:02:14,188 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:02:14,188 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,425 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,436 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,437 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,437 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,439 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,439 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,442 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,443 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,445 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:02:14,445 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:02:14,445 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:02:14,445 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:19:01,114 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:19:01,121 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,357 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,357 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:19:01,357 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:19:01,357 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:19:01,357 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:19:01,357 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,357 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,364 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,364 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,364 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,364 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,364 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,368 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,369 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,371 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,373 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,374 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,377 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,379 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:19:01,380 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:19:01,380 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:19:01,605 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:19:01,605 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,803 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,803 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:19:01,803 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:19:01,803 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:19:01,813 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:19:01,813 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,813 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,813 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,813 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,817 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,818 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,820 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,821 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,822 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,825 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,826 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,827 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,829 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:19:01,830 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:19:01,830 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:19:01,831 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:19:41,884 - anesthesia_qc - INFO - 文件上传成功，开始导入: d:\code\AnesthesiaQCDataManagement\uploads\sample_anesthesia_data.xlsx
2025-08-22 08:19:41,884 - anesthesia_qc - INFO - 开始导入文件: sample_anesthesia_data.xlsx, 批次ID: be05a91b-7e36-4916-ae5e-c2db487b1c44
2025-08-22 08:19:41,891 - anesthesia_qc - INFO - 开始执行: 读取Excel文件
2025-08-22 08:19:41,934 - anesthesia_qc - INFO - 读取到 500 条记录
2025-08-22 08:19:41,934 - anesthesia_qc - INFO - 完成执行: 读取Excel文件 | 耗时: 0.04秒 | 内存变化: +0.2MB
2025-08-22 08:19:41,934 - anesthesia_qc - INFO - 开始执行: 数据清洗
2025-08-22 08:19:41,940 - anesthesia_qc - INFO - 开始清洗麻醉方法...
2025-08-22 08:19:41,947 - anesthesia_qc - INFO - 麻醉方法清洗完成，标准化为 6 种方法
2025-08-22 08:19:41,947 - anesthesia_qc - INFO - 完成执行: 数据清洗 | 耗时: 0.01秒 | 内存变化: +0.6MB
2025-08-22 08:19:41,947 - anesthesia_qc - INFO - 开始执行: 数据脱敏
2025-08-22 08:19:41,947 - anesthesia_qc - INFO - 开始数据脱敏处理（安全模式）
2025-08-22 08:19:41,954 - anesthesia_qc - INFO - 数据脱敏处理完成（安全模式），处理了 500 条记录
2025-08-22 08:19:41,955 - anesthesia_qc - INFO - ⚠️ 原始敏感数据已被永久删除，无法恢复
2025-08-22 08:19:41,955 - anesthesia_qc - INFO - 完成执行: 数据脱敏 | 耗时: 0.01秒 | 内存变化: +0.0MB
2025-08-22 08:19:41,955 - anesthesia_qc - INFO - 开始执行: 导入数据库
2025-08-22 08:19:41,955 - anesthesia_qc - ERROR - 处理第 1 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,955 - anesthesia_qc - ERROR - 处理第 2 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,955 - anesthesia_qc - ERROR - 处理第 3 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,962 - anesthesia_qc - ERROR - 处理第 4 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,962 - anesthesia_qc - ERROR - 处理第 5 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,962 - anesthesia_qc - ERROR - 处理第 6 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,962 - anesthesia_qc - ERROR - 处理第 7 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,962 - anesthesia_qc - ERROR - 处理第 8 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,962 - anesthesia_qc - ERROR - 处理第 9 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,968 - anesthesia_qc - ERROR - 处理第 10 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,968 - anesthesia_qc - ERROR - 处理第 11 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,968 - anesthesia_qc - ERROR - 处理第 12 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,968 - anesthesia_qc - ERROR - 处理第 13 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,968 - anesthesia_qc - ERROR - 处理第 14 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,968 - anesthesia_qc - ERROR - 处理第 15 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,974 - anesthesia_qc - ERROR - 处理第 16 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,976 - anesthesia_qc - ERROR - 处理第 17 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,976 - anesthesia_qc - ERROR - 处理第 18 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,976 - anesthesia_qc - ERROR - 处理第 19 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,976 - anesthesia_qc - ERROR - 处理第 20 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 21 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 22 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 23 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 24 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 25 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 26 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 27 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 28 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,982 - anesthesia_qc - ERROR - 处理第 29 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,990 - anesthesia_qc - ERROR - 处理第 30 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,990 - anesthesia_qc - ERROR - 处理第 31 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,990 - anesthesia_qc - ERROR - 处理第 32 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,990 - anesthesia_qc - ERROR - 处理第 33 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,990 - anesthesia_qc - ERROR - 处理第 34 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,990 - anesthesia_qc - ERROR - 处理第 35 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,996 - anesthesia_qc - ERROR - 处理第 36 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,996 - anesthesia_qc - ERROR - 处理第 37 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,996 - anesthesia_qc - ERROR - 处理第 38 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,996 - anesthesia_qc - ERROR - 处理第 39 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,996 - anesthesia_qc - ERROR - 处理第 40 行数据时出错: no such column: patient_id
2025-08-22 08:19:41,996 - anesthesia_qc - ERROR - 处理第 41 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,002 - anesthesia_qc - ERROR - 处理第 42 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,003 - anesthesia_qc - ERROR - 处理第 43 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,003 - anesthesia_qc - ERROR - 处理第 44 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,003 - anesthesia_qc - ERROR - 处理第 45 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,003 - anesthesia_qc - ERROR - 处理第 46 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,009 - anesthesia_qc - ERROR - 处理第 47 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,010 - anesthesia_qc - ERROR - 处理第 48 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,010 - anesthesia_qc - ERROR - 处理第 49 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,010 - anesthesia_qc - ERROR - 处理第 50 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,010 - anesthesia_qc - ERROR - 处理第 51 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,010 - anesthesia_qc - ERROR - 处理第 52 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,016 - anesthesia_qc - ERROR - 处理第 53 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,017 - anesthesia_qc - ERROR - 处理第 54 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,017 - anesthesia_qc - ERROR - 处理第 55 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,017 - anesthesia_qc - ERROR - 处理第 56 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,017 - anesthesia_qc - ERROR - 处理第 57 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,017 - anesthesia_qc - ERROR - 处理第 58 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,017 - anesthesia_qc - ERROR - 处理第 59 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,024 - anesthesia_qc - ERROR - 处理第 60 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,024 - anesthesia_qc - ERROR - 处理第 61 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,024 - anesthesia_qc - ERROR - 处理第 62 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,024 - anesthesia_qc - ERROR - 处理第 63 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,024 - anesthesia_qc - ERROR - 处理第 64 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,024 - anesthesia_qc - ERROR - 处理第 65 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,024 - anesthesia_qc - ERROR - 处理第 66 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,030 - anesthesia_qc - ERROR - 处理第 67 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,030 - anesthesia_qc - ERROR - 处理第 68 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,030 - anesthesia_qc - ERROR - 处理第 69 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,030 - anesthesia_qc - ERROR - 处理第 70 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,030 - anesthesia_qc - ERROR - 处理第 71 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 72 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 73 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 74 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 75 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 76 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 77 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 78 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 79 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,037 - anesthesia_qc - ERROR - 处理第 80 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 81 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 82 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 83 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 84 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 85 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 86 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 87 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,044 - anesthesia_qc - ERROR - 处理第 88 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 89 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 90 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 91 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 92 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 93 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 94 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 95 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 96 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,051 - anesthesia_qc - ERROR - 处理第 97 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,059 - anesthesia_qc - ERROR - 处理第 98 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,059 - anesthesia_qc - ERROR - 处理第 99 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,059 - anesthesia_qc - ERROR - 处理第 100 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,059 - anesthesia_qc - ERROR - 处理第 101 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,059 - anesthesia_qc - ERROR - 处理第 102 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,065 - anesthesia_qc - ERROR - 处理第 103 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,065 - anesthesia_qc - ERROR - 处理第 104 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,065 - anesthesia_qc - ERROR - 处理第 105 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,065 - anesthesia_qc - ERROR - 处理第 106 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,065 - anesthesia_qc - ERROR - 处理第 107 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,065 - anesthesia_qc - ERROR - 处理第 108 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,065 - anesthesia_qc - ERROR - 处理第 109 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,072 - anesthesia_qc - ERROR - 处理第 110 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,072 - anesthesia_qc - ERROR - 处理第 111 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,072 - anesthesia_qc - ERROR - 处理第 112 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,072 - anesthesia_qc - ERROR - 处理第 113 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,072 - anesthesia_qc - ERROR - 处理第 114 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,072 - anesthesia_qc - ERROR - 处理第 115 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,072 - anesthesia_qc - ERROR - 处理第 116 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 117 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 118 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 119 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 120 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 121 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 122 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 123 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,079 - anesthesia_qc - ERROR - 处理第 124 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 125 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 126 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 127 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 128 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 129 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 130 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 131 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,086 - anesthesia_qc - ERROR - 处理第 132 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,093 - anesthesia_qc - ERROR - 处理第 133 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,094 - anesthesia_qc - ERROR - 处理第 134 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,094 - anesthesia_qc - ERROR - 处理第 135 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,094 - anesthesia_qc - ERROR - 处理第 136 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,094 - anesthesia_qc - ERROR - 处理第 137 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,094 - anesthesia_qc - ERROR - 处理第 138 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,094 - anesthesia_qc - ERROR - 处理第 139 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,094 - anesthesia_qc - ERROR - 处理第 140 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 141 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 142 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 143 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 144 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 145 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 146 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 147 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 148 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,100 - anesthesia_qc - ERROR - 处理第 149 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,107 - anesthesia_qc - ERROR - 处理第 150 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,107 - anesthesia_qc - ERROR - 处理第 151 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,107 - anesthesia_qc - ERROR - 处理第 152 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,107 - anesthesia_qc - ERROR - 处理第 153 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,107 - anesthesia_qc - ERROR - 处理第 154 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,107 - anesthesia_qc - ERROR - 处理第 155 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,107 - anesthesia_qc - ERROR - 处理第 156 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,113 - anesthesia_qc - ERROR - 处理第 157 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 158 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 159 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 160 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 161 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 162 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 163 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 164 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,114 - anesthesia_qc - ERROR - 处理第 165 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 166 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 167 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 168 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 169 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 170 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 171 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 172 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,121 - anesthesia_qc - ERROR - 处理第 173 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,127 - anesthesia_qc - ERROR - 处理第 174 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,128 - anesthesia_qc - ERROR - 处理第 175 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,128 - anesthesia_qc - ERROR - 处理第 176 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,128 - anesthesia_qc - ERROR - 处理第 177 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,128 - anesthesia_qc - ERROR - 处理第 178 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,128 - anesthesia_qc - ERROR - 处理第 179 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,128 - anesthesia_qc - ERROR - 处理第 180 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,128 - anesthesia_qc - ERROR - 处理第 181 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 182 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 183 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 184 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 185 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 186 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 187 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 188 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 189 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 190 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,135 - anesthesia_qc - ERROR - 处理第 191 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 192 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 193 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 194 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 195 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 196 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 197 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 198 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 199 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,142 - anesthesia_qc - ERROR - 处理第 200 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 201 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 202 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 203 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 204 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 205 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 206 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 207 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 208 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,149 - anesthesia_qc - ERROR - 处理第 209 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,155 - anesthesia_qc - ERROR - 处理第 210 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 211 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 212 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 213 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 214 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 215 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 216 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 217 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,156 - anesthesia_qc - ERROR - 处理第 218 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 219 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 220 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 221 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 222 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 223 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 224 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 225 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 226 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,163 - anesthesia_qc - ERROR - 处理第 227 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 228 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 229 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 230 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 231 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 232 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 233 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 234 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 235 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 236 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,169 - anesthesia_qc - ERROR - 处理第 237 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 238 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 239 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 240 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 241 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 242 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 243 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 244 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 245 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 246 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,176 - anesthesia_qc - ERROR - 处理第 247 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 248 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 249 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 250 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 251 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 252 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 253 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 254 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 255 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 256 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,183 - anesthesia_qc - ERROR - 处理第 257 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 258 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 259 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 260 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 261 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 262 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 263 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 264 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,190 - anesthesia_qc - ERROR - 处理第 265 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 266 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 267 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 268 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 269 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 270 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 271 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 272 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,197 - anesthesia_qc - ERROR - 处理第 273 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 274 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 275 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 276 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 277 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 278 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 279 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 280 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,204 - anesthesia_qc - ERROR - 处理第 281 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 282 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 283 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 284 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 285 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 286 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 287 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 288 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,211 - anesthesia_qc - ERROR - 处理第 289 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 290 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 291 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 292 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 293 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 294 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 295 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 296 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,218 - anesthesia_qc - ERROR - 处理第 297 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,225 - anesthesia_qc - ERROR - 处理第 298 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,225 - anesthesia_qc - ERROR - 处理第 299 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,225 - anesthesia_qc - ERROR - 处理第 300 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,225 - anesthesia_qc - ERROR - 处理第 301 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,225 - anesthesia_qc - ERROR - 处理第 302 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,225 - anesthesia_qc - ERROR - 处理第 303 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,225 - anesthesia_qc - ERROR - 处理第 304 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 305 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 306 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 307 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 308 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 309 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 310 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 311 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 312 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,232 - anesthesia_qc - ERROR - 处理第 313 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,240 - anesthesia_qc - ERROR - 处理第 314 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,240 - anesthesia_qc - ERROR - 处理第 315 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,240 - anesthesia_qc - ERROR - 处理第 316 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,240 - anesthesia_qc - ERROR - 处理第 317 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,240 - anesthesia_qc - ERROR - 处理第 318 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,240 - anesthesia_qc - ERROR - 处理第 319 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,240 - anesthesia_qc - ERROR - 处理第 320 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,247 - anesthesia_qc - ERROR - 处理第 321 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,247 - anesthesia_qc - ERROR - 处理第 322 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,247 - anesthesia_qc - ERROR - 处理第 323 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,247 - anesthesia_qc - ERROR - 处理第 324 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,247 - anesthesia_qc - ERROR - 处理第 325 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,247 - anesthesia_qc - ERROR - 处理第 326 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,253 - anesthesia_qc - ERROR - 处理第 327 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,253 - anesthesia_qc - ERROR - 处理第 328 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,253 - anesthesia_qc - ERROR - 处理第 329 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,253 - anesthesia_qc - ERROR - 处理第 330 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,253 - anesthesia_qc - ERROR - 处理第 331 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,253 - anesthesia_qc - ERROR - 处理第 332 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,253 - anesthesia_qc - ERROR - 处理第 333 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 334 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 335 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 336 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 337 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 338 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 339 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 340 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,260 - anesthesia_qc - ERROR - 处理第 341 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 342 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 343 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 344 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 345 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 346 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 347 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 348 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 349 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,267 - anesthesia_qc - ERROR - 处理第 350 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 351 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 352 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 353 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 354 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 355 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 356 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 357 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 358 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,274 - anesthesia_qc - ERROR - 处理第 359 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 360 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 361 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 362 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 363 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 364 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 365 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 366 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,281 - anesthesia_qc - ERROR - 处理第 367 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,288 - anesthesia_qc - ERROR - 处理第 368 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,288 - anesthesia_qc - ERROR - 处理第 369 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,288 - anesthesia_qc - ERROR - 处理第 370 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,288 - anesthesia_qc - ERROR - 处理第 371 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,288 - anesthesia_qc - ERROR - 处理第 372 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,288 - anesthesia_qc - ERROR - 处理第 373 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,288 - anesthesia_qc - ERROR - 处理第 374 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,295 - anesthesia_qc - ERROR - 处理第 375 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,296 - anesthesia_qc - ERROR - 处理第 376 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,296 - anesthesia_qc - ERROR - 处理第 377 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,296 - anesthesia_qc - ERROR - 处理第 378 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,296 - anesthesia_qc - ERROR - 处理第 379 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,296 - anesthesia_qc - ERROR - 处理第 380 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,296 - anesthesia_qc - ERROR - 处理第 381 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,301 - anesthesia_qc - ERROR - 处理第 382 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 383 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 384 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 385 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 386 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 387 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 388 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 389 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,303 - anesthesia_qc - ERROR - 处理第 390 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 391 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 392 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 393 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 394 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 395 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 396 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 397 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 398 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,309 - anesthesia_qc - ERROR - 处理第 399 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 400 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 401 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 402 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 403 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 404 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 405 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 406 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 407 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,316 - anesthesia_qc - ERROR - 处理第 408 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 409 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 410 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 411 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 412 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 413 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 414 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 415 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 416 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,322 - anesthesia_qc - ERROR - 处理第 417 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 418 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 419 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 420 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 421 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 422 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 423 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 424 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 425 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,329 - anesthesia_qc - ERROR - 处理第 426 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 427 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 428 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 429 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 430 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 431 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 432 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 433 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,336 - anesthesia_qc - ERROR - 处理第 434 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,343 - anesthesia_qc - ERROR - 处理第 435 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 436 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 437 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 438 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 439 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 440 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 441 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 442 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 443 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,344 - anesthesia_qc - ERROR - 处理第 444 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 445 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 446 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 447 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 448 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 449 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 450 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 451 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,350 - anesthesia_qc - ERROR - 处理第 452 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 453 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 454 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 455 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 456 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 457 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 458 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 459 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,358 - anesthesia_qc - ERROR - 处理第 460 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 461 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 462 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 463 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 464 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 465 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 466 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 467 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 468 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,364 - anesthesia_qc - ERROR - 处理第 469 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 470 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 471 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 472 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 473 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 474 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 475 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 476 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 477 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 478 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 479 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,371 - anesthesia_qc - ERROR - 处理第 480 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 481 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 482 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 483 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 484 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 485 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 486 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 487 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 488 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,378 - anesthesia_qc - ERROR - 处理第 489 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 490 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 491 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 492 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 493 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 494 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 495 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 496 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 497 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 498 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 499 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - ERROR - 处理第 500 行数据时出错: no such column: patient_id
2025-08-22 08:19:42,385 - anesthesia_qc - INFO - 完成执行: 导入数据库 | 耗时: 0.43秒 | 内存变化: +0.2MB
2025-08-22 08:19:42,410 - anesthesia_qc - INFO - 文件导入完成: {'success': True, 'batch_id': 'be05a91b-7e36-4916-ae5e-c2db487b1c44', 'filename': 'sample_anesthesia_data.xlsx', 'total_records': 500, 'new_records': 0, 'duplicate_records': 0, 'error_records': 500, 'privacy_enabled': True, 'privacy_summary': {'anonymized_names_count': 0, 'anonymized_ids_count': 500, 'field_stats': {}, 'security_mode': True, 'original_data_deleted': True, 'warning': '原始敏感数据已被永久删除，无法恢复'}}
2025-08-22 08:19:42,410 - anesthesia_qc - INFO - import_excel_file 执行时间: 0.53秒
2025-08-22 08:21:45,322 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:21:45,322 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,544 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,554 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,556 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,558 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,559 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,560 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,563 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,564 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,565 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:45,566 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:21:45,566 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:21:45,567 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:21:57,253 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:21:57,253 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,475 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,482 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,483 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,483 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,484 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,486 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,487 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,488 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,490 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,492 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:21:57,493 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:21:57,493 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:21:57,495 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:22:16,439 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:22:16,439 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,672 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,672 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:22:16,672 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:22:16,672 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:22:16,672 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:22:16,672 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,682 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,682 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,682 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,682 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,686 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,687 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,688 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,689 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,691 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,693 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,694 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,696 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:16,698 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:22:16,698 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:22:16,698 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:22:34,076 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:22:34,076 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,292 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,298 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,299 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,306 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,308 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,309 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,311 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,313 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,314 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,315 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,317 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:22:34,318 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:22:34,318 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:22:34,537 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:22:34,542 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,794 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,794 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:22:34,794 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:22:34,794 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:22:34,794 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:22:34,794 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,794 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,799 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,800 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,801 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,803 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,804 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,805 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,806 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,808 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,809 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,811 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,812 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:22:34,814 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:22:34,814 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:22:34,814 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:22:53,538 - anesthesia_qc - INFO - 文件上传成功，开始导入: d:\code\AnesthesiaQCDataManagement\uploads\sample_anesthesia_data.xlsx
2025-08-22 08:22:53,539 - anesthesia_qc - INFO - 开始导入文件: sample_anesthesia_data.xlsx, 批次ID: ea265bc2-32e4-49a0-88ef-89cd4eb17f56
2025-08-22 08:22:53,550 - anesthesia_qc - INFO - 开始执行: 读取Excel文件
2025-08-22 08:22:53,588 - anesthesia_qc - INFO - 读取到 500 条记录
2025-08-22 08:22:53,588 - anesthesia_qc - INFO - 完成执行: 读取Excel文件 | 耗时: 0.05秒 | 内存变化: +0.0MB
2025-08-22 08:22:53,588 - anesthesia_qc - INFO - 开始执行: 数据清洗
2025-08-22 08:22:53,592 - anesthesia_qc - INFO - 开始清洗麻醉方法...
2025-08-22 08:22:53,599 - anesthesia_qc - INFO - 麻醉方法清洗完成，标准化为 6 种方法
2025-08-22 08:22:53,599 - anesthesia_qc - INFO - 完成执行: 数据清洗 | 耗时: 0.01秒 | 内存变化: +0.5MB
2025-08-22 08:22:53,599 - anesthesia_qc - INFO - 开始执行: 数据脱敏
2025-08-22 08:22:53,599 - anesthesia_qc - INFO - 开始数据脱敏处理（安全模式）
2025-08-22 08:22:53,599 - anesthesia_qc - INFO - 数据脱敏处理完成（安全模式），处理了 500 条记录
2025-08-22 08:22:53,599 - anesthesia_qc - INFO - ⚠️ 原始敏感数据已被永久删除，无法恢复
2025-08-22 08:22:53,599 - anesthesia_qc - INFO - 完成执行: 数据脱敏 | 耗时: 0.00秒 | 内存变化: +0.1MB
2025-08-22 08:22:53,606 - anesthesia_qc - INFO - 开始执行: 导入数据库
2025-08-22 08:22:59,113 - anesthesia_qc - INFO - 完成执行: 导入数据库 | 耗时: 5.50秒 | 内存变化: +0.4MB
2025-08-22 08:22:59,130 - anesthesia_qc - INFO - 文件导入完成: {'success': True, 'batch_id': 'ea265bc2-32e4-49a0-88ef-89cd4eb17f56', 'filename': 'sample_anesthesia_data.xlsx', 'total_records': 500, 'new_records': 500, 'duplicate_records': 0, 'error_records': 0, 'privacy_enabled': True, 'privacy_summary': {'anonymized_names_count': 0, 'anonymized_ids_count': 500, 'field_stats': {}, 'security_mode': True, 'original_data_deleted': True, 'warning': '原始敏感数据已被永久删除，无法恢复'}}
2025-08-22 08:22:59,130 - anesthesia_qc - INFO - import_excel_file 执行时间: 5.59秒
2025-08-22 08:22:59,132 - anesthesia_qc - WARNING - 慢请求: /api/import-data - 5595.30ms
2025-08-22 08:23:27,101 - anesthesia_qc - INFO - 文件上传成功，开始导入: d:\code\AnesthesiaQCDataManagement\uploads\test_import.xlsx
2025-08-22 08:23:27,101 - anesthesia_qc - INFO - 开始导入文件: test_import.xlsx, 批次ID: 764d4b2b-c74b-40ea-b556-cbae379e4ddb
2025-08-22 08:23:27,111 - anesthesia_qc - INFO - 开始执行: 读取Excel文件
2025-08-22 08:23:27,116 - anesthesia_qc - INFO - 读取到 3 条记录
2025-08-22 08:23:27,116 - anesthesia_qc - INFO - 完成执行: 读取Excel文件 | 耗时: 0.01秒 | 内存变化: +0.0MB
2025-08-22 08:23:27,118 - anesthesia_qc - INFO - 开始执行: 数据清洗
2025-08-22 08:23:27,118 - anesthesia_qc - INFO - 开始清洗麻醉方法...
2025-08-22 08:23:27,121 - anesthesia_qc - INFO - 麻醉方法清洗完成，标准化为 2 种方法
2025-08-22 08:23:27,123 - anesthesia_qc - INFO - 完成执行: 数据清洗 | 耗时: 0.00秒 | 内存变化: +0.0MB
2025-08-22 08:23:27,123 - anesthesia_qc - INFO - 开始执行: 数据脱敏
2025-08-22 08:23:27,124 - anesthesia_qc - INFO - 开始数据脱敏处理（安全模式）
2025-08-22 08:23:27,125 - anesthesia_qc - INFO - 脱敏处理：anesthesiologist
2025-08-22 08:23:27,125 - anesthesia_qc - INFO - 脱敏处理：surgeon
2025-08-22 08:23:27,126 - anesthesia_qc - INFO - 数据脱敏处理完成（安全模式），处理了 3 条记录
2025-08-22 08:23:27,126 - anesthesia_qc - INFO - ⚠️ 原始敏感数据已被永久删除，无法恢复
2025-08-22 08:23:27,127 - anesthesia_qc - INFO - 完成执行: 数据脱敏 | 耗时: 0.00秒 | 内存变化: +0.0MB
2025-08-22 08:23:27,127 - anesthesia_qc - INFO - 开始执行: 导入数据库
2025-08-22 08:23:27,160 - anesthesia_qc - INFO - 完成执行: 导入数据库 | 耗时: 0.03秒 | 内存变化: +0.0MB
2025-08-22 08:23:27,178 - anesthesia_qc - INFO - 文件导入完成: {'success': True, 'batch_id': '764d4b2b-c74b-40ea-b556-cbae379e4ddb', 'filename': 'test_import.xlsx', 'total_records': 3, 'new_records': 3, 'duplicate_records': 0, 'error_records': 0, 'privacy_enabled': True, 'privacy_summary': {'anonymized_names_count': 7, 'anonymized_ids_count': 503, 'field_stats': {}, 'security_mode': True, 'original_data_deleted': True, 'warning': '原始敏感数据已被永久删除，无法恢复'}}
2025-08-22 08:23:27,178 - anesthesia_qc - INFO - import_excel_file 执行时间: 0.08秒
2025-08-22 08:26:20,054 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:26:20,064 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,277 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,277 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:26:20,277 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:26:20,277 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:26:20,277 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:26:20,287 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,287 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,287 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,287 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,291 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,291 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,292 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,293 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,294 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,295 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,296 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,297 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,298 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:26:20,300 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:26:20,300 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:26:20,301 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:27:14,303 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:27:14,303 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,535 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,536 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:27:14,536 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:27:14,536 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:27:14,537 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:27:14,538 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,539 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,539 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,540 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,543 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,544 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,545 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,546 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,547 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,549 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,551 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,552 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,554 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:14,556 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:27:14,556 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:27:14,557 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:27:29,347 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:27:29,347 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,572 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,579 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,580 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,581 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,581 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,582 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,583 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,585 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,586 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,587 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,588 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:29,590 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:27:29,590 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:27:29,591 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:27:51,701 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:27:51,701 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,917 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,917 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:27:51,917 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:27:51,917 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:27:51,917 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:27:51,917 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,917 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,925 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,925 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,925 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,925 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,925 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,925 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,925 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,930 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,932 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,933 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,935 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:51,937 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:27:51,937 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:27:51,937 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:27:52,153 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:27:52,160 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,383 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,383 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:27:52,383 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:27:52,383 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:27:52,383 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:27:52,390 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,390 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,390 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,390 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,394 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,395 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,396 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,397 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,398 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,401 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,402 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,404 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,404 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:27:52,407 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:27:52,408 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:27:52,408 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:29:57,827 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:29:57,827 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,045 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,054 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,056 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,057 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,058 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,058 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,059 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,060 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,061 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,062 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,063 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,064 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:29:58,065 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:29:58,065 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:29:58,571 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:29:58,581 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,798 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,807 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,807 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,808 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,810 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,811 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,812 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,812 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,815 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:29:58,815 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:29:58,815 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:29:58,815 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:30:43,420 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:30:43,430 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,626 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:30:43,636 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,666 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,667 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:30:43,667 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:30:43,667 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:30:43,668 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:30:43,668 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,669 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,670 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,671 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,673 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,673 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,674 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,675 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,676 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,678 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,679 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,680 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,681 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,683 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:30:43,683 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:30:43,683 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,875 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,883 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,884 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,885 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,885 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,887 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,888 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,889 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,891 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,892 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,893 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:30:43,894 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:30:43,894 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:30:43,895 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:31:12,056 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:31:12,066 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,276 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,276 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:31:12,276 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:31:12,276 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:31:12,286 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:31:12,286 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,286 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,286 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,290 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,290 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,291 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,292 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,293 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,294 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,296 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,298 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,299 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,300 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:12,301 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:31:12,301 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:31:12,301 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:31:12,829 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:31:12,839 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,049 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,055 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,056 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,057 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,057 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,058 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,060 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,061 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,063 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,064 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,067 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:13,069 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:31:13,069 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:31:13,069 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:31:27,081 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:31:27,081 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,302 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,311 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,311 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,312 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,313 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,314 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,315 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,316 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,317 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,318 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:27,319 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:31:27,319 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:31:27,320 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:31:27,789 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:31:27,789 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,040 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,047 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,048 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,048 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,049 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,050 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,051 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,053 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,053 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,053 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,053 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:28,053 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:31:28,053 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:31:28,053 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:31:46,898 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:31:46,908 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,147 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,156 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,158 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,159 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,160 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,161 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,162 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,164 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,165 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,166 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,168 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:31:47,169 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:31:47,169 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:31:47,223 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:31:47,225 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,456 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,465 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,466 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,467 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,468 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,468 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,470 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,471 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,473 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,474 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:31:47,475 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:31:47,475 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:31:47,476 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:32:29,050 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:32:29,050 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,272 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,272 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:32:29,272 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:32:29,272 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:32:29,272 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:32:29,272 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,272 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,279 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,279 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,279 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,279 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,279 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,283 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,284 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,285 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,287 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,288 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,288 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,288 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:32:29,288 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:32:29,292 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:32:29,494 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 08:32:29,494 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,710 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,710 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 08:32:29,710 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 08:32:29,710 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 08:32:29,710 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 08:32:29,717 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,717 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,717 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,721 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,722 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,722 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,724 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,725 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,726 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,726 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,726 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,731 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,732 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 08:32:29,734 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 08:32:29,734 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 08:32:29,735 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 08:32:55,321 - anesthesia_qc - INFO - 文件上传成功，开始导入: d:\code\AnesthesiaQCDataManagement\uploads\test_optimized_import.xlsx
2025-08-22 08:32:55,321 - anesthesia_qc - INFO - 🚀 开始导入文件: test_optimized_import.xlsx, 批次ID: 0651fb6a-4afc-4347-baf9-1780ab85d41d, 脱敏: True
2025-08-22 08:32:55,335 - anesthesia_qc - INFO - 开始执行: 读取Excel文件
2025-08-22 08:32:55,509 - anesthesia_qc - INFO - 📊 读取到 4 条记录
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 完成执行: 读取Excel文件 | 耗时: 0.18秒 | 内存变化: +10.6MB
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 开始执行: 数据清洗
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 开始清洗麻醉方法...
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 麻醉方法清洗完成，标准化为 2 种方法
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 🧹 数据清洗完成，有效记录: 4
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 完成执行: 数据清洗 | 耗时: 0.00秒 | 内存变化: +0.6MB
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 开始执行: 导入数据库
2025-08-22 08:32:55,510 - anesthesia_qc - INFO - 🔄 开始处理和导入数据，共 4 条记录，脱敏模式: True
2025-08-22 08:32:55,560 - anesthesia_qc - INFO - ✅ 数据处理完成: 新增4条, 重复0条, 错误0条
2025-08-22 08:32:55,560 - anesthesia_qc - INFO - 完成执行: 导入数据库 | 耗时: 0.05秒 | 内存变化: +0.2MB
2025-08-22 08:32:55,580 - anesthesia_qc - INFO - ✅ 文件导入完成: 新增4条, 重复0条, 错误0条
2025-08-22 08:32:55,580 - anesthesia_qc - INFO - import_excel_file 执行时间: 0.26秒
2025-08-22 09:23:10,422 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 09:23:10,429 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,658 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,658 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 09:23:10,658 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 09:23:10,658 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 09:23:10,658 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 09:23:10,658 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,658 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,665 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,665 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,665 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,665 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,665 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,671 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,671 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,673 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,675 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,677 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,678 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:10,682 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 09:23:10,682 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 09:23:10,682 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 09:23:10,888 - anesthesia_qc - INFO - 缓存清理中间件已启动
2025-08-22 09:23:10,894 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,094 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,094 - anesthesia_qc - INFO - 数据清洗器初始化完成
2025-08-22 09:23:11,094 - anesthesia_qc - INFO - 数据脱敏器初始化完成
2025-08-22 09:23:11,094 - anesthesia_qc - INFO - 数据处理管道初始化完成
2025-08-22 09:23:11,094 - anesthesia_qc - INFO - 数据导入服务初始化完成
2025-08-22 09:23:11,094 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,094 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,104 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,105 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,106 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,107 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,108 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,109 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,111 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,114 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,117 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,119 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,122 - anesthesia_qc - INFO - 数据库初始化完成
2025-08-22 09:23:11,122 - anesthesia_qc - INFO - Flask性能优化配置已应用
2025-08-22 09:23:11,124 - anesthesia_qc - INFO - Flask性能中间件已启用
2025-08-22 09:23:11,124 - anesthesia_qc - INFO - 优化的JSON编码器已设置
2025-08-22 09:23:31,253 - anesthesia_qc - INFO - 根据模式 '*surgery*' 删除了 0 个缓存条目
2025-08-22 09:23:31,253 - anesthesia_qc - INFO - 根据模式 '*dashboard*' 删除了 0 个缓存条目
2025-08-22 09:23:31,253 - anesthesia_qc - INFO - 根据模式 '*stats*' 删除了 0 个缓存条目
2025-08-22 09:23:31,253 - anesthesia_qc - INFO - 根据模式 '*comprehensive*' 删除了 0 个缓存条目
2025-08-22 09:23:31,253 - anesthesia_qc - INFO - surgery_records delete 操作清理了 0 个缓存条目
2025-08-22 09:23:31,253 - anesthesia_qc - INFO - 批量删除 100 条手术记录成功，清理了 0 个缓存条目
2025-08-22 09:23:34,773 - anesthesia_qc - INFO - 根据模式 '*surgery*' 删除了 0 个缓存条目
2025-08-22 09:23:34,773 - anesthesia_qc - INFO - 根据模式 '*dashboard*' 删除了 0 个缓存条目
2025-08-22 09:23:34,774 - anesthesia_qc - INFO - 根据模式 '*stats*' 删除了 0 个缓存条目
2025-08-22 09:23:34,774 - anesthesia_qc - INFO - 根据模式 '*comprehensive*' 删除了 0 个缓存条目
2025-08-22 09:23:34,774 - anesthesia_qc - INFO - surgery_records delete 操作清理了 0 个缓存条目
2025-08-22 09:23:34,774 - anesthesia_qc - INFO - 批量删除 100 条手术记录成功，清理了 0 个缓存条目
2025-08-22 09:23:37,744 - anesthesia_qc - INFO - 根据模式 '*surgery*' 删除了 0 个缓存条目
2025-08-22 09:23:37,744 - anesthesia_qc - INFO - 根据模式 '*dashboard*' 删除了 0 个缓存条目
2025-08-22 09:23:37,744 - anesthesia_qc - INFO - 根据模式 '*stats*' 删除了 0 个缓存条目
2025-08-22 09:23:37,744 - anesthesia_qc - INFO - 根据模式 '*comprehensive*' 删除了 0 个缓存条目
2025-08-22 09:23:37,744 - anesthesia_qc - INFO - surgery_records delete 操作清理了 0 个缓存条目
2025-08-22 09:23:37,744 - anesthesia_qc - INFO - 批量删除 100 条手术记录成功，清理了 0 个缓存条目
2025-08-22 09:23:40,611 - anesthesia_qc - INFO - 根据模式 '*surgery*' 删除了 0 个缓存条目
2025-08-22 09:23:40,611 - anesthesia_qc - INFO - 根据模式 '*dashboard*' 删除了 0 个缓存条目
2025-08-22 09:23:40,611 - anesthesia_qc - INFO - 根据模式 '*stats*' 删除了 0 个缓存条目
2025-08-22 09:23:40,611 - anesthesia_qc - INFO - 根据模式 '*comprehensive*' 删除了 0 个缓存条目
2025-08-22 09:23:40,611 - anesthesia_qc - INFO - surgery_records delete 操作清理了 0 个缓存条目
2025-08-22 09:23:40,611 - anesthesia_qc - INFO - 批量删除 100 条手术记录成功，清理了 0 个缓存条目
2025-08-22 09:23:43,338 - anesthesia_qc - INFO - 根据模式 '*surgery*' 删除了 0 个缓存条目
2025-08-22 09:23:43,338 - anesthesia_qc - INFO - 根据模式 '*dashboard*' 删除了 0 个缓存条目
2025-08-22 09:23:43,338 - anesthesia_qc - INFO - 根据模式 '*stats*' 删除了 0 个缓存条目
2025-08-22 09:23:43,338 - anesthesia_qc - INFO - 根据模式 '*comprehensive*' 删除了 0 个缓存条目
2025-08-22 09:23:43,338 - anesthesia_qc - INFO - surgery_records delete 操作清理了 0 个缓存条目
2025-08-22 09:23:43,338 - anesthesia_qc - INFO - 批量删除 100 条手术记录成功，清理了 0 个缓存条目
2025-08-22 09:23:46,776 - anesthesia_qc - INFO - 根据模式 '*surgery*' 删除了 0 个缓存条目
2025-08-22 09:23:46,777 - anesthesia_qc - INFO - 根据模式 '*dashboard*' 删除了 0 个缓存条目
2025-08-22 09:23:46,778 - anesthesia_qc - INFO - 根据模式 '*stats*' 删除了 0 个缓存条目
2025-08-22 09:23:46,778 - anesthesia_qc - INFO - 根据模式 '*comprehensive*' 删除了 0 个缓存条目
2025-08-22 09:23:46,778 - anesthesia_qc - INFO - surgery_records delete 操作清理了 0 个缓存条目
2025-08-22 09:23:46,778 - anesthesia_qc - INFO - 批量删除 7 条手术记录成功，清理了 0 个缓存条目
2025-08-22 09:23:54,140 - anesthesia_qc - INFO - 文件上传成功，开始导入: d:\code\AnesthesiaQCDataManagement\uploads\sample_anesthesia_data.xlsx
2025-08-22 09:23:54,141 - anesthesia_qc - INFO - 🚀 开始导入文件: sample_anesthesia_data.xlsx, 批次ID: b6920d40-7a2e-445d-a961-febfa1aa7ae3, 脱敏: True
2025-08-22 09:23:54,149 - anesthesia_qc - INFO - 开始执行: 读取Excel文件
2025-08-22 09:23:54,191 - anesthesia_qc - INFO - 📊 读取到 500 条记录
2025-08-22 09:23:54,191 - anesthesia_qc - ERROR - 执行失败: 读取Excel文件 | 耗时: 0.04秒 | 错误: 缺少必要字段: ['patient_id', 'patient_name', 'surgery_date']
2025-08-22 09:23:54,191 - anesthesia_qc - ERROR - ❌ 文件导入失败: 缺少必要字段: ['patient_id', 'patient_name', 'surgery_date']
2025-08-22 09:23:54,198 - anesthesia_qc - INFO - import_excel_file 执行时间: 0.06秒
