"""
API路由模块
"""
from flask import Blueprint, request, jsonify
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename

# 导入服务
from services.data_service import DataImportService
from statistics.statistics_service import StatisticsService
from database.models import DatabaseManager
from config.settings import UPLOAD_DIR, ALLOWED_EXTENSIONS
from utils.logger import logger
from utils.cache import cached
from utils.error_handler import handle_web_error

# 创建蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

# 初始化服务
data_service = DataImportService()
stats_service = StatisticsService()
db_manager = DatabaseManager()


def allowed_file(filename):
    """检查文件类型是否允许"""
    if '.' not in filename:
        return False
    ext = '.' + filename.rsplit('.', 1)[1].lower()
    return ext in ALLOWED_EXTENSIONS


def convert_to_json_serializable(obj):
    """转换为JSON可序列化的对象"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    else:
        return obj


@api_bp.route('/dashboard-data')
@handle_web_error
@cached(ttl=300, key_prefix="dashboard_")  # 缓存5分钟
def get_dashboard_data():
    """获取仪表盘数据 - 优化版本"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 如果没有指定日期，默认获取最近30天的数据
    if not start_date or not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

    try:
        # 使用简化的查询获取关键数据
        with db_manager.get_connection() as conn:
            # 基础统计 - 单次查询获取多个指标
            cursor = conn.execute('''
                SELECT
                    COUNT(*) as total_surgeries,
                    COUNT(DISTINCT patient_id) as total_patients,
                    AVG(duration_minutes) as avg_duration
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
            ''', (start_date, end_date))

            basic_stats = cursor.fetchone()

            # 麻醉方法分布 - 限制结果数量
            cursor = conn.execute('''
                SELECT anesthesia_method, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
                  AND anesthesia_method IS NOT NULL
                  AND anesthesia_method != ''
                GROUP BY anesthesia_method
                ORDER BY count DESC
                LIMIT 5
            ''', (start_date, end_date))

            anesthesia_methods = {row[0]: row[1] for row in cursor.fetchall()}

            # 手术类型分布 - 限制结果数量
            cursor = conn.execute('''
                SELECT surgery_type, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
                  AND surgery_type IS NOT NULL
                  AND surgery_type != ''
                GROUP BY surgery_type
                ORDER BY count DESC
                LIMIT 5
            ''', (start_date, end_date))

            surgery_types = {row[0]: row[1] for row in cursor.fetchall()}

            # 科室分布 - 限制结果数量
            cursor = conn.execute('''
                SELECT department, COUNT(*) as count
                FROM surgery_records
                WHERE surgery_date BETWEEN ? AND ?
                  AND department IS NOT NULL
                  AND department != ''
                GROUP BY department
                ORDER BY count DESC
                LIMIT 5
            ''', (start_date, end_date))

            department_distribution = {row[0]: row[1] for row in cursor.fetchall()}

        # 构建响应数据
        dashboard_data = {
            'overview': {
                'total_surgeries': basic_stats[0] if basic_stats else 0,
                'total_patients': basic_stats[1] if basic_stats else 0,
                'avg_duration': round(basic_stats[2], 1) if basic_stats and basic_stats[2] else 0
            },
            'anesthesia_methods': anesthesia_methods,
            'surgery_types': surgery_types,
            'department_distribution': department_distribution,
            'age_distribution': {},  # 简化版本暂时为空
            'gender_distribution': {},  # 简化版本暂时为空
            'daily_trend': [],  # 简化版本暂时为空
            'quality_score': {'overall_score': 85.0}  # 默认值
        }

        return jsonify({
            'success': True,
            'data': convert_to_json_serializable(dashboard_data)
        })

    except Exception as e:
        logger.error(f"获取仪表盘数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@api_bp.route('/import-data', methods=['POST'])
@handle_web_error
def import_data():
    """导入数据API"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    if not allowed_file(file.filename):
        return jsonify({'success': False, 'error': '不支持的文件格式'})
    
    # 获取导入选项
    privacy_enabled = request.form.get('enable_privacy', 'false').lower() == 'true'
    
    # 保存上传的文件
    filename = secure_filename(file.filename)
    file_path = UPLOAD_DIR / filename
    file.save(file_path)
    
    logger.info(f"文件上传成功，开始导入: {file_path}")
    
    try:
        # 导入数据
        result = data_service.import_excel_file(
            str(file_path), 
            privacy_enabled=privacy_enabled
        )
        
        # 清理上传的文件
        os.remove(file_path)
        
        return jsonify(convert_to_json_serializable(result))
        
    except Exception as e:
        # 确保清理上传的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise e


@api_bp.route('/preview-data', methods=['POST'])
@handle_web_error
def preview_data():
    """预览数据API"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    if not allowed_file(file.filename):
        return jsonify({'success': False, 'error': '不支持的文件格式'})
    
    # 保存上传的文件
    filename = secure_filename(file.filename)
    file_path = UPLOAD_DIR / filename
    file.save(file_path)
    
    try:
        # 快速预览 - 只读取前几行数据
        max_rows = int(request.form.get('max_rows', 10))

        # 先读取少量数据获取基本信息
        df_sample = pd.read_excel(file_path, nrows=max_rows)

        # 获取完整行数（快速方法）
        try:
            # 尝试快速获取总行数
            df_full = pd.read_excel(file_path, usecols=[0])  # 只读第一列来计算行数
            total_rows = len(df_full)
        except:
            # 如果失败，使用样本数据
            total_rows = len(df_sample)

        # 获取基本信息
        file_info = {
            'total_rows': total_rows,
            'total_columns': len(df_sample.columns),
            'columns': df_sample.columns.tolist(),
            'file_size': os.path.getsize(file_path),
            'sheet_name': 'Sheet1'  # 默认工作表名
        }

        # 获取预览数据
        preview_data = []
        for index, row in df_sample.iterrows():
            row_data = {}
            for col in df_sample.columns:
                value = row[col]
                if pd.isna(value):
                    row_data[col] = None
                else:
                    row_data[col] = str(value)
            preview_data.append(row_data)
        
        # 检查敏感列
        sensitive_columns = []
        potential_sensitive = ['患者姓名', '患者ID', '住院号', '病案号', '手机号', '联系电话', '身份证号']
        for col in potential_sensitive:
            if col in df_sample.columns:
                sensitive_columns.append(col)
        
        # 清理上传的文件
        os.remove(file_path)
        
        result = {
            'success': True,
            'file_info': file_info,
            'preview_data': preview_data,
            'sensitive_columns': sensitive_columns,
            'preview_rows': len(preview_data)
        }
        
        return jsonify(convert_to_json_serializable(result))
        
    except Exception as e:
        # 确保清理上传的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise e


@api_bp.route('/import-history')
@handle_web_error
def get_import_history():
    """获取导入历史"""
    limit = int(request.args.get('limit', 20))
    history = db_manager.get_recent_imports(limit)
    return jsonify(convert_to_json_serializable(history))


@api_bp.route('/statistics')
@handle_web_error
@cached(ttl=300, key_prefix="stats_api_")  # 缓存5分钟
def get_statistics():
    """获取统计数据"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    analysis_type = request.args.get('type', 'dashboard')

    try:
        if analysis_type == 'dashboard':
            # 获取仪表盘数据
            stats = stats_service.get_dashboard_data(start_date, end_date)
        elif analysis_type == 'comprehensive':
            # 获取综合分析数据
            stats = stats_service.get_comprehensive_analysis(start_date, end_date)
        else:
            # 获取自定义分析数据
            stats = stats_service.get_custom_analysis(analysis_type, start_date, end_date)

        return jsonify({
            'success': True,
            'data': convert_to_json_serializable(stats)
        })

    except Exception as e:
        logger.error(f"获取统计数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
