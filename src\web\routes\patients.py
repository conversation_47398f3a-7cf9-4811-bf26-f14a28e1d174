"""
患者管理API路由模块
"""
from flask import Blueprint, request, jsonify
from database.models import DatabaseManager
from utils.logger import logger
from utils.error_handler import handle_web_error

# 创建蓝图
patients_bp = Blueprint('patients', __name__, url_prefix='/api/patients')

# 初始化数据库管理器
db_manager = DatabaseManager()


def convert_to_json_serializable(obj):
    """转换为JSON可序列化的对象"""
    import numpy as np
    import pandas as pd
    from datetime import datetime
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    else:
        return obj


@patients_bp.route('')
@handle_web_error
def get_patients():
    """获取患者列表（分页）"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 25))
        sort_field = request.args.get('sort_field', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        search = request.args.get('search', '')

        # 筛选参数
        min_age = request.args.get('min_age', '')
        max_age = request.args.get('max_age', '')
        gender = request.args.get('gender', '')
        data_status = request.args.get('data_status', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 构建查询条件
        conditions = []
        params = []

        if search:
            conditions.append('''(
                COALESCE(p.masked_name, p.original_name) LIKE ? OR
                COALESCE(p.masked_id, p.original_id) LIKE ? OR
                p.phone LIKE ?
            )''')
            search_param = f'%{search}%'
            params.extend([search_param] * 3)

        if min_age:
            conditions.append('p.age >= ?')
            params.append(int(min_age))

        if max_age:
            conditions.append('p.age <= ?')
            params.append(int(max_age))

        if gender:
            conditions.append('p.gender = ?')
            params.append(gender)

        if data_status == 'masked':
            conditions.append('p.masked_name IS NOT NULL')
        elif data_status == 'normal':
            conditions.append('p.masked_name IS NULL')

        if start_date:
            conditions.append('p.created_at >= ?')
            params.append(start_date)

        if end_date:
            conditions.append('p.created_at <= ?')
            params.append(end_date + ' 23:59:59')

        # 构建WHERE子句
        where_clause = ''
        if conditions:
            where_clause = 'WHERE ' + ' AND '.join(conditions)

        # 验证排序字段
        valid_sort_fields = ['patient_id', 'original_name', 'masked_name', 'age', 'gender', 'contact', 'created_at']
        if sort_field not in valid_sort_fields:
            sort_field = 'created_at'

        if sort_order not in ['asc', 'desc']:
            sort_order = 'desc'

        # 获取总记录数
        count_query = f'''
            SELECT COUNT(*)
            FROM patients p
            {where_clause}
        '''

        with db_manager.get_connection() as conn:
            cursor = conn.execute(count_query, params)
            total_records = cursor.fetchone()[0]

        # 计算分页信息
        total_pages = (total_records + page_size - 1) // page_size
        offset = (page - 1) * page_size

        # 获取患者数据和手术次数 - 使用实际的数据库结构
        data_query = f'''
            SELECT
                p.id,
                COALESCE(p.masked_id, p.original_id) as patient_id,
                COALESCE(p.masked_name, p.original_name) as name,
                p.age,
                p.gender,
                p.phone as contact,
                p.created_at,
                p.updated_at,
                COUNT(sr.id) as surgery_count
            FROM patients p
            LEFT JOIN surgery_records sr ON p.id = sr.patient_id
            {where_clause}
            GROUP BY p.id
            ORDER BY p.{sort_field} {sort_order.upper()}
            LIMIT ? OFFSET ?
        '''

        with db_manager.get_connection() as conn:
            cursor = conn.execute(data_query, params + [page_size, offset])
            patients = [dict(row) for row in cursor.fetchall()]

        # 构建分页信息
        pagination = {
            'current_page': page,
            'total_pages': total_pages,
            'total_records': total_records,
            'page_size': page_size,
            'has_prev': page > 1,
            'has_next': page < total_pages
        }

        return jsonify({
            'success': True,
            'patients': convert_to_json_serializable(patients),
            'pagination': pagination
        })

    except Exception as e:
        logger.error(f"获取患者列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@patients_bp.route('/<int:patient_id>')
@handle_web_error
def get_patient_detail(patient_id):
    """获取患者详情"""
    try:
        with db_manager.get_connection() as conn:
            # 获取患者基本信息
            cursor = conn.execute('''
                SELECT p.*, COUNT(sr.id) as surgery_count
                FROM patients p
                LEFT JOIN surgery_records sr ON p.id = sr.patient_id
                WHERE p.id = ?
                GROUP BY p.id
            ''', (patient_id,))

            patient = cursor.fetchone()
            if not patient:
                return jsonify({
                    'success': False,
                    'error': '患者不存在'
                }), 404

            patient_data = dict(patient)

            # 获取患者的手术记录
            cursor = conn.execute('''
                SELECT id, surgery_date, anesthesia_method, surgery_type, department, duration_minutes
                FROM surgery_records
                WHERE patient_id = ?
                ORDER BY surgery_date DESC
            ''', (patient_id,))

            surgeries = [dict(row) for row in cursor.fetchall()]
            patient_data['surgeries'] = surgeries

        return jsonify({
            'success': True,
            'patient': convert_to_json_serializable(patient_data)
        })
    except Exception as e:
        logger.error(f"获取患者详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@patients_bp.route('/<int:patient_id>', methods=['DELETE'])
@handle_web_error
def delete_patient(patient_id):
    """删除患者"""
    try:
        with db_manager.get_connection() as conn:
            # 检查患者是否存在
            cursor = conn.execute('SELECT id FROM patients WHERE id = ?', (patient_id,))
            if not cursor.fetchone():
                return jsonify({
                    'success': False,
                    'error': '患者不存在'
                }), 404

            # 检查是否有关联的手术记录
            cursor = conn.execute('SELECT COUNT(*) FROM surgery_records WHERE patient_id = ?', (patient_id,))
            surgery_count = cursor.fetchone()[0]

            if surgery_count > 0:
                return jsonify({
                    'success': False,
                    'error': f'无法删除患者，该患者有 {surgery_count} 条手术记录'
                }), 400

            # 删除患者
            cursor = conn.execute('DELETE FROM patients WHERE id = ?', (patient_id,))
            conn.commit()

            if cursor.rowcount > 0:
                return jsonify({
                    'success': True,
                    'message': '患者删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '删除失败'
                }), 500

    except Exception as e:
        logger.error(f"删除患者失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
