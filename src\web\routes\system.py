"""
系统管理路由模块
"""
from flask import Blueprint, request, jsonify
from database.models import DatabaseManager
from utils.logger import logger
from utils.error_handler import handle_web_error

# 创建蓝图
system_bp = Blueprint('system', __name__, url_prefix='/api')

# 初始化数据库管理器
db_manager = DatabaseManager()


def convert_to_json_serializable(obj):
    """转换为JSON可序列化的对象"""
    import numpy as np
    import pandas as pd
    from datetime import datetime
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    else:
        return obj


@system_bp.route('/system-info')
@handle_web_error
def get_system_info():
    """获取系统信息"""
    import psutil
    
    try:
        # 获取系统资源信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 获取数据库统计
        stats = db_manager.get_statistics()
        
        system_info = {
            'cpu_usage': cpu_percent,
            'memory_usage': memory.percent,
            'memory_total': memory.total,
            'memory_used': memory.used,
            'disk_usage': disk.percent,
            'disk_total': disk.total,
            'disk_used': disk.used,
            'database_stats': stats
        }
        
        return jsonify({
            'success': True,
            'data': convert_to_json_serializable(system_info)
        })
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取系统信息失败'
        })


@system_bp.route('/process-file', methods=['POST'])
@handle_web_error
def process_file_with_pipeline():
    """使用处理管道处理文件"""
    try:
        from services.data_service import DataImportService
        
        data = request.get_json()
        file_path = data.get('file_path')
        clean_data = data.get('clean_data', True)
        anonymize_data = data.get('anonymize_data', True)
        import_to_db = data.get('import_to_db', True)
        output_dir = data.get('output_dir', 'output')

        if not file_path:
            return jsonify({
                'success': False,
                'error': '缺少文件路径参数'
            }), 400

        # 使用数据导入服务的新处理管道
        data_service = DataImportService()
        result = data_service.process_file_with_pipeline(
            file_path=file_path,
            clean_data=clean_data,
            anonymize_data=anonymize_data,
            import_to_db=import_to_db,
            output_dir=output_dir
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"处理文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@system_bp.route('/validate-file', methods=['POST'])
@handle_web_error
def validate_file():
    """验证文件格式和内容"""
    try:
        from services.data_service import DataImportService
        
        data = request.get_json()
        file_path = data.get('file_path')

        if not file_path:
            return jsonify({
                'success': False,
                'error': '缺少文件路径参数'
            }), 400

        # 使用处理管道验证文件
        data_service = DataImportService()
        validation_result = data_service.processing_pipeline.validate_file(file_path)

        return jsonify({
            'success': True,
            'validation_result': validation_result
        })

    except Exception as e:
        logger.error(f"文件验证失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@system_bp.route('/processing-config')
@handle_web_error
def get_processing_config():
    """获取处理配置"""
    try:
        from services.data_pipeline import create_default_config
        config = create_default_config()

        return jsonify({
            'success': True,
            'config': config
        })

    except Exception as e:
        logger.error(f"获取处理配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
