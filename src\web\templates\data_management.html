{% extends "base.html" %}

{% block title %}数据管理 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}数据管理{% endblock %}

{% block extra_css %}
<style>
/* 排序相关样式 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background-color 0.2s ease;
}

.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sort-icon {
    opacity: 0.6;
    transition: all 0.2s ease;
    font-size: 0.8em;
}

.sortable:hover .sort-icon {
    opacity: 1;
}

.sortable.sort-asc .sort-icon {
    opacity: 1;
    color: #28a745;
}

.sortable.sort-desc .sort-icon {
    opacity: 1;
    color: #dc3545;
}

.sortable.sort-asc .sort-icon::before {
    content: "\f0de"; /* fa-sort-up */
}

.sortable.sort-desc .sort-icon::before {
    content: "\f0dd"; /* fa-sort-down */
}

/* 表格行悬停效果增强 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

/* 排序指示器动画 */
@keyframes sortIndicator {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.sortable.sorting .sort-icon {
    animation: sortIndicator 0.3s ease;
}

/* 加载状态样式 */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
}

.table-loading tbody {
    position: relative;
}

.table-loading tbody::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
{% endblock %}

{% block content %}
<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>
                    数据查询
                </h5>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-2">
                            <label for="quickFilter" class="form-label">快速筛选</label>
                            <select class="form-select" id="quickFilter">
                                <option value="">自定义时间</option>
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="this_week">本周</option>
                                <option value="last_week">上周</option>
                                <option value="this_month">本月</option>
                                <option value="last_month">上个月</option>
                                <option value="this_quarter">本季度</option>
                                <option value="last_quarter">上季度</option>
                                <option value="this_year">今年</option>
                                <option value="last_year">去年</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="startDate" class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-2">
                            <label for="endDate" class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-3">
                            <label for="anesthesiaMethod" class="form-label">麻醉方式</label>
                            <select class="form-select" id="anesthesiaMethod">
                                <option value="">全部</option>
                                <option value="全身麻醉">全身麻醉</option>
                                <option value="椎管内麻醉">椎管内麻醉</option>
                                <option value="神经阻滞麻醉">神经阻滞麻醉</option>
                                <option value="静脉麻醉">静脉麻醉</option>
                                <option value="局部麻醉">局部麻醉</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="surgeryType" class="form-label">手术类型</label>
                            <select class="form-select" id="surgeryType">
                                <option value="">全部</option>
                                <option value="择期手术">择期手术</option>
                                <option value="急诊手术">急诊手术</option>
                                <option value="日间手术">日间手术</option>
                                <option value="门诊手术">门诊手术</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="searchKeyword" class="form-label">关键词搜索</label>
                            <input type="text" class="form-control" id="searchKeyword" placeholder="搜索患者ID、医生姓名等...">
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                搜索
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="resetSearch()">
                                <i class="fas fa-undo me-1"></i>
                                重置
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>
                                导出
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 数据统计概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card info">
            <h3 id="totalRecords">-</h3>
            <p>总记录数</p>
            <i class="fas fa-database"></i>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card success">
            <h3 id="filteredRecords">-</h3>
            <p>筛选结果</p>
            <i class="fas fa-filter"></i>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card warning">
            <h3 id="uniquePatients">-</h3>
            <p>患者数量</p>
            <i class="fas fa-users"></i>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card primary">
            <h3 id="dateRange">-</h3>
            <p>时间跨度</p>
            <i class="fas fa-calendar-alt"></i>
        </div>
    </div>
</div>

<!-- 数据表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    手术记录
                </h5>
                <div class="d-flex align-items-center">
                    <div id="sortIndicator" class="me-3" style="display: none;">
                        <small class="text-muted">
                            <i class="fas fa-sort-amount-down me-1"></i>
                            <span id="sortText">按手术日期排序</span>
                        </small>
                    </div>
                    <label for="pageSize" class="form-label me-2 mb-0">每页显示:</label>
                    <select class="form-select form-select-sm" id="pageSize" style="width: auto;">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div id="loadingIndicator" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载数据...</p>
                </div>
                
                <div id="dataTable" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>序号</th>
                                    <th class="sortable" data-sort="patient_id">
                                        患者ID
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="patient_name">
                                        患者姓名
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="surgery_date">
                                        手术日期
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="anesthesia_method">
                                        麻醉方式
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="surgery_type">
                                        手术类型
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="postoperative_analgesia">
                                        术后镇痛
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="anesthesiologist">
                                        麻醉医生
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th class="sortable" data-sort="duration_minutes">
                                        手术时长
                                        <i class="fas fa-sort sort-icon ms-1"></i>
                                    </th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="dataTableBody">
                                <!-- 数据行将在这里动态生成 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="数据分页">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- 分页按钮将在这里动态生成 -->
                        </ul>
                    </nav>
                </div>
                
                <div id="noDataMessage" class="text-center py-4" style="display: none;">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无数据</h5>
                    <p class="text-muted">请调整搜索条件或导入新数据</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="recordDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>
                    记录详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="recordDetailContent">
                <!-- 详情内容将在这里动态生成 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 使用立即执行函数避免全局变量冲突
(function() {
    'use strict';

    // 检查是否已经初始化过
    if (window.dataManagementInitialized) {
        return;
    }
    window.dataManagementInitialized = true;

let currentPage = 1;
let pageSize = 25;
let totalRecords = 0;
let currentData = [];
let currentSort = {
    field: null,
    direction: null // 'asc' or 'desc'
};

document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期范围（今年全年，确保有数据显示）
    const now = new Date();
    const startDate = new Date(now.getFullYear(), 0, 1); // 今年1月1日
    const endDate = new Date(now.getFullYear(), 11, 31); // 今年12月31日

    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

    // 设置快速筛选为"今年"
    document.getElementById('quickFilter').value = 'this_year';
    
    // 绑定事件
    document.getElementById('searchForm').addEventListener('submit', handleSearch);
    document.getElementById('pageSize').addEventListener('change', function() {
        pageSize = parseInt(this.value);
        currentPage = 1;
        loadData();
    });

    // 快速筛选事件
    document.getElementById('quickFilter').addEventListener('change', function() {
        const value = this.value;
        if (value) {
            const dates = getQuickFilterDates(value);
            document.getElementById('startDate').value = dates.start;
            document.getElementById('endDate').value = dates.end;
            // 自动触发搜索
            currentPage = 1;
            loadData();
        }
    });

    // 初始化排序功能
    initializeSorting();

    // 初始加载数据
    loadData();
});

// 排序功能
function initializeSorting() {
    const sortableHeaders = document.querySelectorAll('.sortable');

    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const field = this.getAttribute('data-sort');
            handleSort(field, this);
        });
    });
}

function handleSort(field, headerElement) {
    // 添加排序动画
    headerElement.classList.add('sorting');
    setTimeout(() => headerElement.classList.remove('sorting'), 300);

    // 确定排序方向
    let direction = 'asc';
    if (currentSort.field === field) {
        if (currentSort.direction === 'asc') {
            direction = 'desc';
        } else if (currentSort.direction === 'desc') {
            // 第三次点击取消排序
            direction = null;
            field = null;
        }
    }

    // 更新排序状态
    currentSort.field = field;
    currentSort.direction = direction;

    // 更新UI
    updateSortUI();

    // 重新加载数据
    currentPage = 1;
    loadData();
}

function updateSortUI() {
    // 清除所有排序状态
    document.querySelectorAll('.sortable').forEach(header => {
        header.classList.remove('sort-asc', 'sort-desc');
        const icon = header.querySelector('.sort-icon');
        if (icon) {
            icon.className = 'fas fa-sort sort-icon ms-1';
        }
    });

    // 设置当前排序状态
    if (currentSort.field && currentSort.direction) {
        const currentHeader = document.querySelector(`[data-sort="${currentSort.field}"]`);
        if (currentHeader) {
            currentHeader.classList.add(`sort-${currentSort.direction}`);
            const icon = currentHeader.querySelector('.sort-icon');
            if (icon) {
                icon.className = `fas fa-sort-${currentSort.direction === 'asc' ? 'up' : 'down'} sort-icon ms-1`;
            }
        }

        // 显示排序指示器
        const sortIndicator = document.getElementById('sortIndicator');
        const sortText = document.getElementById('sortText');
        if (sortIndicator && sortText) {
            const fieldNames = {
                'patient_id': '患者ID',
                'patient_name': '患者姓名',
                'surgery_date': '手术日期',
                'anesthesia_method': '麻醉方式',
                'surgery_type': '手术类型',
                'postoperative_analgesia': '术后镇痛',
                'anesthesiologist': '麻醉医生',
                'duration_minutes': '手术时长'
            };

            const fieldName = fieldNames[currentSort.field] || currentSort.field;
            const directionText = currentSort.direction === 'asc' ? '升序' : '降序';
            sortText.textContent = `按${fieldName}${directionText}`;
            sortIndicator.style.display = 'block';
        }
    } else {
        // 隐藏排序指示器
        const sortIndicator = document.getElementById('sortIndicator');
        if (sortIndicator) {
            sortIndicator.style.display = 'none';
        }
    }
}

function sortData(data, field, direction) {
    if (!field || !direction) {
        return data;
    }

    return data.sort((a, b) => {
        let aVal = a[field];
        let bVal = b[field];

        // 处理空值
        if (aVal === null || aVal === undefined || aVal === '') aVal = '';
        if (bVal === null || bVal === undefined || bVal === '') bVal = '';

        // 数字类型排序
        if (field === 'duration_minutes') {
            aVal = parseFloat(aVal) || 0;
            bVal = parseFloat(bVal) || 0;
        }
        // 日期类型排序
        else if (field === 'surgery_date') {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
        }
        // 字符串类型排序
        else {
            aVal = String(aVal).toLowerCase();
            bVal = String(bVal).toLowerCase();
        }

        let result = 0;
        if (aVal < bVal) result = -1;
        else if (aVal > bVal) result = 1;

        return direction === 'desc' ? -result : result;
    });
}

function handleSearch(event) {
    event.preventDefault();
    currentPage = 1;
    loadData();
}

function resetSearch() {
    document.getElementById('searchForm').reset();
    
    // 重置日期范围
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    
    currentPage = 1;
    loadData();
}

function loadData() {
    showLoading();
    
    // 构建查询参数
    const params = new URLSearchParams({
        page: currentPage,
        page_size: pageSize,
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value,
        anesthesia_method: document.getElementById('anesthesiaMethod').value,
        surgery_type: document.getElementById('surgeryType').value,
        keyword: document.getElementById('searchKeyword').value
    });

    // 添加排序参数
    if (currentSort.field && currentSort.direction) {
        params.append('sort_field', currentSort.field);
        params.append('sort_direction', currentSort.direction);
    }
    
    // 调用真实API
    fetch(`/api/surgery-records?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(result => {
            console.log('API响应:', result);

            if (result.success && result.data) {
                console.log('数据结构:', result.data);
                updateDataTable(result.data);
                updateStatistics(result.data);
            } else {
                console.error('获取数据失败:', result);
                const errorMsg = result.error || '未知错误';
                showError('获取数据失败: ' + errorMsg);

                // 显示空数据状态
                updateDataTable({ records: [], total: 0 });
                updateStatistics({ records: [], total: 0 });
            }
        })
        .catch(error => {
            console.error('网络错误:', error);
            showError('网络错误: ' + error.message);

            // 显示空数据状态
            updateDataTable({ records: [], total: 0 });
            updateStatistics({ records: [], total: 0 });
        })
        .finally(() => {
            hideLoading();
        });
}

function generateMockData() {
    // 生成模拟数据
    const data = [];
    const anesthesiaMethods = ['全身麻醉', '椎管内麻醉', '神经阻滞麻醉', '静脉麻醉'];
    const surgeryTypes = ['择期手术', '急诊手术', '日间手术', '门诊手术'];
    const analgesiaTypes = ['PCIA', 'PCEA', 'PCNA', '口服镇痛', '无镇痛'];
    
    for (let i = 1; i <= pageSize; i++) {
        const recordNum = (currentPage - 1) * pageSize + i;
        data.push({
            id: recordNum,
            patient_id: `P${String(recordNum).padStart(4, '0')}`,
            patient_name: `患者${recordNum}`,
            surgery_date: new Date(2025, 0, Math.floor(Math.random() * 30) + 1).toISOString().split('T')[0],
            anesthesia_method: anesthesiaMethods[Math.floor(Math.random() * anesthesiaMethods.length)],
            surgery_type: surgeryTypes[Math.floor(Math.random() * surgeryTypes.length)],
            postoperative_analgesia: analgesiaTypes[Math.floor(Math.random() * analgesiaTypes.length)],
            anesthesiologist: `医生${Math.floor(Math.random() * 10) + 1}`,
            duration_minutes: Math.floor(Math.random() * 300) + 60
        });
    }
    
    return {
        records: data,
        total: 1000, // 模拟总记录数
        page: currentPage,
        page_size: pageSize,
        total_pages: Math.ceil(1000 / pageSize)
    };
}

function updateDataTable(data) {
    currentData = data.records;
    totalRecords = data.total;

    // 应用客户端排序
    let sortedRecords = [...data.records];
    if (currentSort.field && currentSort.direction) {
        sortedRecords = sortData(sortedRecords, currentSort.field, currentSort.direction);
    }

    const tbody = document.getElementById('dataTableBody');
    tbody.innerHTML = '';

    if (sortedRecords.length === 0) {
        document.getElementById('dataTable').style.display = 'none';
        document.getElementById('noDataMessage').style.display = 'block';
        return;
    }

    document.getElementById('noDataMessage').style.display = 'none';
    document.getElementById('dataTable').style.display = 'block';
    
    sortedRecords.forEach((record, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${(currentPage - 1) * pageSize + index + 1}</td>
            <td>${record.patient_id}</td>
            <td>${record.patient_name}</td>
            <td>${record.surgery_date}</td>
            <td>${record.anesthesia_method}</td>
            <td>${record.surgery_type}</td>
            <td>${record.postoperative_analgesia}</td>
            <td>${record.anesthesiologist}</td>
            <td>${record.duration_minutes}分钟</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewRecord(${record.id})">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="editRecord(${record.id})">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    updatePagination(data);
}

function updatePagination(data) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    const totalPages = data.total_pages;
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
            <i class="fas fa-chevron-left"></i>
        </a>
    `;
    pagination.appendChild(prevLi);
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
            <i class="fas fa-chevron-right"></i>
        </a>
    `;
    pagination.appendChild(nextLi);
}

function changePage(page) {
    if (page < 1 || page > Math.ceil(totalRecords / pageSize)) return;
    currentPage = page;
    loadData();
}

function getQuickFilterDates(filterType) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (filterType) {
        case 'today':
            return {
                start: formatDate(today),
                end: formatDate(today)
            };

        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return {
                start: formatDate(yesterday),
                end: formatDate(yesterday)
            };

        case 'this_week':
            const thisWeekStart = new Date(today);
            thisWeekStart.setDate(today.getDate() - today.getDay());
            const thisWeekEnd = new Date(thisWeekStart);
            thisWeekEnd.setDate(thisWeekStart.getDate() + 6);
            return {
                start: formatDate(thisWeekStart),
                end: formatDate(thisWeekEnd)
            };

        case 'last_week':
            const lastWeekStart = new Date(today);
            lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
            const lastWeekEnd = new Date(lastWeekStart);
            lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
            return {
                start: formatDate(lastWeekStart),
                end: formatDate(lastWeekEnd)
            };

        case 'this_month':
            const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            return {
                start: formatDate(thisMonthStart),
                end: formatDate(thisMonthEnd)
            };

        case 'last_month':
            const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
            return {
                start: formatDate(lastMonthStart),
                end: formatDate(lastMonthEnd)
            };

        case 'this_quarter':
            const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
            const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);
            return {
                start: formatDate(quarterStart),
                end: formatDate(quarterEnd)
            };

        case 'last_quarter':
            const lastQuarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 - 3, 1);
            const lastQuarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 0);
            return {
                start: formatDate(lastQuarterStart),
                end: formatDate(lastQuarterEnd)
            };

        case 'this_year':
            const thisYearStart = new Date(now.getFullYear(), 0, 1);
            const thisYearEnd = new Date(now.getFullYear(), 11, 31);
            return {
                start: formatDate(thisYearStart),
                end: formatDate(thisYearEnd)
            };

        case 'last_year':
            const lastYearStart = new Date(now.getFullYear() - 1, 0, 1);
            const lastYearEnd = new Date(now.getFullYear() - 1, 11, 31);
            return {
                start: formatDate(lastYearStart),
                end: formatDate(lastYearEnd)
            };

        default:
            return { start: '', end: '' };
    }
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function updateStatistics(data) {
    try {
        // 防御性编程：检查数据结构
        if (!data) {
            console.warn('updateStatistics: data is null or undefined');
            return;
        }

        // 获取总记录数，支持多种可能的属性名
        const total = data.total || data.total_records || 0;
        const records = data.records || [];

        // 更新统计卡片
        const totalElement = document.getElementById('totalRecords');
        if (totalElement) {
            totalElement.textContent = formatNumber(total);
        }

        const filteredElement = document.getElementById('filteredRecords');
        if (filteredElement) {
            filteredElement.textContent = formatNumber(records.length);
        }

        const uniqueElement = document.getElementById('uniquePatients');
        if (uniqueElement) {
            // 计算唯一患者数
            const uniquePatients = new Set(records.map(r => r.patient_id)).size;
            uniqueElement.textContent = formatNumber(uniquePatients);
        }

        const dateElement = document.getElementById('dateRange');
        if (dateElement) {
            // 计算实际的日期范围
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                dateElement.textContent = `${diffDays}天`;
            } else {
                dateElement.textContent = '全部';
            }
        }

    } catch (error) {
        console.error('updateStatistics error:', error);
        console.error('data:', data);
    }
}

function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('dataTable').style.display = 'none';
    document.getElementById('noDataMessage').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

function viewRecord(recordId) {
    const record = currentData.find(r => r.id === recordId);
    if (!record) return;
    
    const content = document.getElementById('recordDetailContent');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>患者ID:</strong></td><td>${record.patient_id}</td></tr>
                    <tr><td><strong>患者姓名:</strong></td><td>${record.patient_name}</td></tr>
                    <tr><td><strong>手术日期:</strong></td><td>${record.surgery_date}</td></tr>
                    <tr><td><strong>手术时长:</strong></td><td>${record.duration_minutes}分钟</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>医疗信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>麻醉方式:</strong></td><td>${record.anesthesia_method}</td></tr>
                    <tr><td><strong>手术类型:</strong></td><td>${record.surgery_type}</td></tr>
                    <tr><td><strong>术后镇痛:</strong></td><td>${record.postoperative_analgesia}</td></tr>
                    <tr><td><strong>麻醉医生:</strong></td><td>${record.anesthesiologist}</td></tr>
                </table>
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('recordDetailModal')).show();
}

function editRecord(recordId) {
    showAlert('编辑功能开发中...', 'info');
}

function exportData() {
    showAlert('正在导出数据...', 'info');
    // 实际应用中这里会调用导出API
    setTimeout(() => {
        showAlert('数据导出完成！', 'success');
    }, 2000);
}

function showError(message) {
    // 显示错误消息
    if (typeof showAlert === 'function') {
        showAlert(message, 'error');
    } else {
        alert(message);
    }
}

function formatNumber(num) {
    // 格式化数字，添加千位分隔符
    if (num === null || num === undefined || isNaN(num)) {
        return '0';
    }

    return Number(num).toLocaleString();
}

})(); // 结束立即执行函数
</script>
{% endblock %}
