/**
 * Chart.js 诊断和错误处理工具
 */

// Chart.js 错误诊断
function diagnoseChartJS() {
    console.log('🔍 Chart.js 诊断开始...');
    
    // 检查 Chart.js 是否加载
    if (typeof Chart === 'undefined') {
        console.error('❌ Chart.js 未加载');
        return false;
    }
    
    console.log('✅ Chart.js 已加载，版本:', Chart.version);
    
    // 检查 ChartDataLabels 插件
    if (typeof ChartDataLabels === 'undefined') {
        console.warn('⚠️ ChartDataLabels 插件未加载');
    } else {
        console.log('✅ ChartDataLabels 插件已加载');
    }
    
    // 检查可用的图表类型
    const availableTypes = Object.keys(Chart.registry.controllers.items);
    console.log('📊 可用的图表类型:', availableTypes);
    
    // 检查是否有 horizontalBar (应该没有)
    if (availableTypes.includes('horizontalBar')) {
        console.warn('⚠️ 发现过时的 horizontalBar 类型');
    } else {
        console.log('✅ 没有过时的 horizontalBar 类型');
    }
    
    // 检查 bar 类型是否可用
    if (availableTypes.includes('bar')) {
        console.log('✅ bar 图表类型可用');
    } else {
        console.error('❌ bar 图表类型不可用');
    }
    
    return true;
}

// 安全的图表创建函数
function createChartSafely(ctx, config) {
    try {
        // 检查配置中是否有过时的类型
        if (config.type === 'horizontalBar') {
            console.warn('⚠️ 检测到过时的 horizontalBar 类型，自动转换为 bar + indexAxis');
            config.type = 'bar';
            if (!config.options) config.options = {};
            config.options.indexAxis = 'y';
        }
        
        // 确保插件已注册
        if (typeof ChartDataLabels !== 'undefined' && !Chart.registry.plugins.get('datalabels')) {
            Chart.register(ChartDataLabels);
            console.log('✅ 自动注册 ChartDataLabels 插件');
        }
        
        // 创建图表
        const chart = new Chart(ctx, config);
        console.log('✅ 图表创建成功:', config.type);
        return chart;
        
    } catch (error) {
        console.error('❌ 图表创建失败:', error);
        console.error('配置:', config);
        
        // 尝试创建一个简单的备用图表
        try {
            const fallbackConfig = {
                type: 'bar',
                data: {
                    labels: ['暂无数据'],
                    datasets: [{
                        label: '数据',
                        data: [0],
                        backgroundColor: 'rgba(54, 162, 235, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '图表加载失败，显示备用图表'
                        }
                    }
                }
            };
            
            const fallbackChart = new Chart(ctx, fallbackConfig);
            console.log('✅ 创建备用图表成功');
            return fallbackChart;
            
        } catch (fallbackError) {
            console.error('❌ 备用图表也创建失败:', fallbackError);
            return null;
        }
    }
}

// 图表配置验证器
function validateChartConfig(config) {
    const issues = [];
    
    // 检查图表类型
    if (!config.type) {
        issues.push('缺少图表类型 (type)');
    } else if (config.type === 'horizontalBar') {
        issues.push('使用了过时的 horizontalBar 类型，应使用 bar + indexAxis: "y"');
    }
    
    // 检查数据
    if (!config.data) {
        issues.push('缺少数据 (data)');
    } else {
        if (!config.data.labels) {
            issues.push('缺少数据标签 (data.labels)');
        }
        if (!config.data.datasets || config.data.datasets.length === 0) {
            issues.push('缺少数据集 (data.datasets)');
        }
    }
    
    // 检查选项
    if (config.options) {
        // 检查过时的 scales 配置
        if (config.options.scales) {
            if (config.options.scales.xAxes || config.options.scales.yAxes) {
                issues.push('使用了过时的 scales.xAxes/yAxes，应使用 scales.x/y');
            }
        }
        
        // 检查过时的 tooltips 配置
        if (config.options.tooltips) {
            issues.push('使用了过时的 tooltips，应使用 tooltip');
        }
    }
    
    return issues;
}

// 修复图表配置
function fixChartConfig(config) {
    const fixedConfig = JSON.parse(JSON.stringify(config)); // 深拷贝
    
    // 修复图表类型
    if (fixedConfig.type === 'horizontalBar') {
        fixedConfig.type = 'bar';
        if (!fixedConfig.options) fixedConfig.options = {};
        fixedConfig.options.indexAxis = 'y';
    }
    
    // 修复 scales 配置
    if (fixedConfig.options && fixedConfig.options.scales) {
        const scales = fixedConfig.options.scales;
        
        // xAxes -> x
        if (scales.xAxes && Array.isArray(scales.xAxes) && scales.xAxes.length > 0) {
            scales.x = scales.xAxes[0];
            delete scales.xAxes;
        }
        
        // yAxes -> y
        if (scales.yAxes && Array.isArray(scales.yAxes) && scales.yAxes.length > 0) {
            scales.y = scales.yAxes[0];
            delete scales.yAxes;
        }
    }
    
    // 修复 tooltips -> tooltip
    if (fixedConfig.options && fixedConfig.options.tooltips) {
        fixedConfig.options.tooltip = fixedConfig.options.tooltips;
        delete fixedConfig.options.tooltips;
    }
    
    return fixedConfig;
}

// 全局错误处理
window.addEventListener('error', function(event) {
    if (event.message && event.message.includes('horizontalBar')) {
        console.error('🚨 检测到 horizontalBar 错误:', event.message);
        console.log('💡 解决方案: 将 type: "horizontalBar" 改为 type: "bar" 并添加 indexAxis: "y"');
    }
});

// 在页面加载时自动诊断
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(diagnoseChartJS, 100);
});

// 导出函数供全局使用
window.ChartDiagnostics = {
    diagnose: diagnoseChartJS,
    createSafely: createChartSafely,
    validate: validateChartConfig,
    fix: fixChartConfig
};
