"""
重构后的Web应用 - 综合管理平台
"""
import sys
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from flask import Flask, render_template
from datetime import timedelta

# 导入配置和工具
from config.settings import UPLOAD_DIR, MAX_FILE_SIZE
from utils.logger import logger

# 导入路由模块
from web.routes.data_management import data_management_bp
from web.routes.api import api_bp
from web.routes.surgery_records import surgery_records_bp
from web.routes.patients import patients_bp
from web.routes.statistics import statistics_bp
from web.routes.system import system_bp

app = Flask(__name__, static_url_path='/static', static_folder='static')

# Flask性能优化配置
app.config.update({
    'MAX_CONTENT_LENGTH': MAX_FILE_SIZE,
    'SEND_FILE_MAX_AGE_DEFAULT': 31536000,  # 1年缓存
    'PERMANENT_SESSION_LIFETIME': timedelta(hours=24),

    # JSON优化
    'JSON_SORT_KEYS': False,
    'JSONIFY_PRETTYPRINT_REGULAR': False,
    'JSON_AS_ASCII': False,

    # 模板优化
    'TEMPLATES_AUTO_RELOAD': True,  # 开发环境启用模板热加载
    'EXPLAIN_TEMPLATE_LOADING': False,

    # 会话优化
    'SESSION_COOKIE_HTTPONLY': True,
    'SESSION_COOKIE_SECURE': False,  # 开发环境
    'SESSION_COOKIE_SAMESITE': 'Lax',

    # 其他优化
    'PRESERVE_CONTEXT_ON_EXCEPTION': False,
    'TRAP_HTTP_EXCEPTIONS': False,
    'TRAP_BAD_REQUEST_ERRORS': False
})

# 导入Flask性能优化
from web.flask_performance import enable_flask_performance, setup_optimized_json

# 确保上传目录存在
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 启用Flask性能优化
enable_flask_performance(app)
setup_optimized_json(app)

# 注册蓝图
app.register_blueprint(data_management_bp)
app.register_blueprint(api_bp)
app.register_blueprint(surgery_records_bp)
app.register_blueprint(patients_bp)
app.register_blueprint(statistics_bp)
app.register_blueprint(system_bp)


# ==================== 自定义静态文件处理 ====================

@app.route('/static/<path:filename>')
def optimized_static(filename):
    """优化的静态文件服务"""
    from flask import send_from_directory, make_response, request
    import os
    import mimetypes

    # 获取文件路径
    static_folder = app.static_folder
    file_path = os.path.join(static_folder, filename)

    # 检查文件是否存在
    if not os.path.exists(file_path):
        return "File not found", 404

    # 获取文件信息
    file_stat = os.stat(file_path)
    file_size = file_stat.st_size
    file_mtime = file_stat.st_mtime

    # 生成ETag
    etag = f'"{int(file_mtime)}-{file_size}"'

    # 检查If-None-Match头
    if request.headers.get('If-None-Match') == etag:
        response = make_response('', 304)
        response.headers['ETag'] = etag
        return response

    # 发送文件
    response = send_from_directory(static_folder, filename)

    # 设置缓存头
    response.headers['Cache-Control'] = 'public, max-age=31536000, immutable'
    response.headers['ETag'] = etag
    response.headers['Expires'] = 'Thu, 31 Dec 2037 23:55:55 GMT'

    # 设置正确的MIME类型
    mimetype, _ = mimetypes.guess_type(filename)
    if mimetype:
        response.headers['Content-Type'] = mimetype

    return response


# ==================== 页面路由 ====================

@app.route('/')
@app.route('/dashboard')
def dashboard():
    """仪表盘页面"""
    return render_template('dashboard.html')


@app.route('/data-import')
def data_import():
    """数据导入页面"""
    return render_template('data_import.html')


@app.route('/statistics')
def statistics():
    """统计分析页面"""
    return render_template('statistics.html')


@app.route('/system-settings')
def system_settings():
    """系统设置页面"""
    return render_template('system_settings.html')


@app.route('/import-history')
def import_history():
    """导入历史页面"""
    return render_template('import_history.html')


@app.route('/patients')
def patients():
    """患者管理页面"""
    return render_template('patients.html')


@app.route('/patients/<int:patient_id>')
def patient_detail(patient_id):
    """患者详情页面"""
    return render_template('patient_detail.html', patient_id=patient_id)


@app.route('/patients/<int:patient_id>/edit')
def patient_edit(patient_id):
    """患者编辑页面"""
    return render_template('patient_edit.html', patient_id=patient_id)


@app.route('/patients/add')
def patient_add():
    """新增患者页面"""
    return render_template('patient_add.html')


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=True, use_debugger=True)
