<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}麻醉质控数据管理平台{% endblock %}</title>
    
    <!-- 预连接到CDN以提高加载速度 -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- 资源加载器 - 优先加载 -->
    <script src="{{ url_for('static', filename='js/resource-loader.js') }}" defer></script>
    <!-- 页面优化器 -->
    <script src="{{ url_for('static', filename='js/page-optimizer.js') }}" defer></script>

    <!-- 关键CSS - 内联以避免阻塞 -->
    <style>
        /* 关键样式 - 避免FOUC */
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; }
        .loading { opacity: 0.5; pointer-events: none; }
        .lazy { opacity: 0; transition: opacity 0.3s; }
        .lazy.loaded { opacity: 1; }
    </style>

    <!-- Bootstrap CSS - 异步加载 -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"></noscript>

    <!-- Font Awesome - 延迟加载 -->
    <script>
        // 延迟加载Font Awesome
        setTimeout(() => {
            if (window.ResourceLoader) {
                window.ResourceLoader.loadCSS('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
            }
        }, 100);
    </script>
    
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-text: #2c3e50;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
        }
        
        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar.collapsed {
            transform: translateX(-100%);
        }
        
        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-header h4 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.2rem 0;
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: var(--secondary-color);
        }
        
        .nav-link.active {
            color: white;
            background-color: rgba(52, 152, 219, 0.2);
            border-left-color: var(--secondary-color);
        }
        
        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
        }
        
        /* 主内容区域 */
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }
        
        .main-content.expanded {
            margin-left: 0;
        }
        
        /* 顶部导航栏 */
        .top-navbar {
            background: white;
            height: var(--header-height);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            padding: 0 1.5rem;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--dark-text);
            margin-right: 1rem;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-text);
            margin: 0;
        }
        
        .navbar-actions {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        /* 内容区域 */
        .content-area {
            padding: 2rem;
        }
        
        /* 卡片样式 */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #5dade2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            border: none;
        }
        
        /* 统计卡片 */
        .stat-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 10px;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stat-card.primary { background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%); }
        .stat-card.success { background: linear-gradient(135deg, var(--success-color) 0%, #2ecc71 100%); }
        .stat-card.warning { background: linear-gradient(135deg, var(--warning-color) 0%, #f1c40f 100%); }
        .stat-card.danger { background: linear-gradient(135deg, var(--danger-color) 0%, #ec7063 100%); }
        .stat-card.info { background: linear-gradient(135deg, var(--secondary-color) 0%, #5dade2 100%); }
        
        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }
        
        .stat-card p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .stat-card i {
            font-size: 3rem;
            opacity: 0.3;
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
        }
        
        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 1rem 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content-area {
                padding: 1rem;
            }
        }
        
        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 自定义滚动条 */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }
        
        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-heartbeat me-2"></i>麻醉质控平台</h4>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-item">
                <a href="/" class="nav-link" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/data-import" class="nav-link" data-page="data-import">
                    <i class="fas fa-upload"></i>
                    <span>数据导入</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/data-management" class="nav-link" data-page="data-management">
                    <i class="fas fa-database"></i>
                    <span>数据管理</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/patients" class="nav-link" data-page="patients">
                    <i class="fas fa-user-injured"></i>
                    <span>患者管理</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/statistics" class="nav-link" data-page="statistics">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计分析</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/import-history" class="nav-link" data-page="import-history">
                    <i class="fas fa-history"></i>
                    <span>导入历史</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="/system-settings" class="nav-link" data-page="system-settings">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="page-title">{% block page_title %}仪表盘{% endblock %}</h1>
            
            <div class="navbar-actions">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        管理员
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>个人设置</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-question-circle me-2"></i>帮助文档</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- 异步加载Chart.js和相关插件 -->
    <script>
        // 异步加载Chart.js
        function loadChartJS() {
            return new Promise((resolve, reject) => {
                if (typeof Chart !== 'undefined') {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
                script.onload = () => resolve();
                script.onerror = () => reject(new Error('Chart.js加载失败'));
                document.head.appendChild(script);
            });
        }

        // 异步加载Chart.js数据标签插件
        function loadChartDataLabels() {
            return new Promise((resolve, reject) => {
                if (typeof ChartDataLabels !== 'undefined') {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2';
                script.onload = () => resolve();
                script.onerror = () => reject(new Error('ChartDataLabels插件加载失败'));
                document.head.appendChild(script);
            });
        }

        // 异步加载诊断工具
        function loadChartDiagnostics() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = '{{ url_for("static", filename="js/chart-diagnostics.js") }}';
                script.onload = () => resolve();
                script.onerror = () => reject(new Error('Chart诊断工具加载失败'));
                document.head.appendChild(script);
            });
        }

        // 初始化Chart.js相关资源
        window.initChartJS = async function() {
            try {
                await loadChartJS();
                await loadChartDataLabels();
                await loadChartDiagnostics();
                console.log('✅ Chart.js资源加载完成');

                // 触发自定义事件，通知其他脚本Chart.js已准备就绪
                window.dispatchEvent(new CustomEvent('chartjs-ready'));
            } catch (error) {
                console.error('❌ Chart.js资源加载失败:', error);
            }
        };

        // 智能加载Chart.js
        document.addEventListener('DOMContentLoaded', function() {
            // 检查页面是否需要Chart.js
            const needsChartJS = document.querySelector('canvas') ||
                               window.location.pathname.includes('dashboard') ||
                               window.location.pathname.includes('statistics') ||
                               document.querySelector('[data-chart]');

            if (needsChartJS) {
                // 延迟加载以避免阻塞页面渲染
                setTimeout(() => {
                    window.initChartJS();
                }, 100);
            }
        });

        // 动态加载Chart.js的函数（供其他页面调用）
        window.loadChartJSIfNeeded = function() {
            if (typeof Chart === 'undefined') {
                return window.initChartJS();
            }
            return Promise.resolve();
        };
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });
        
        // 导航链接激活状态
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
        
        // 响应式处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                if (sidebar.classList.contains('collapsed')) {
                    mainContent.classList.add('expanded');
                } else {
                    mainContent.classList.remove('expanded');
                }
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        });
        
        // 全局工具函数
        function showAlert(message, type = 'info', duration = 3000) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, duration);
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatNumber(num) {
            return new Intl.NumberFormat('zh-CN').format(num);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
