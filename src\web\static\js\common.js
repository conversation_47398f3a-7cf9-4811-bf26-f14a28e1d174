/**
 * 通用工具函数和组件
 */

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// API请求封装
class ApiClient {
    constructor(baseURL = '') {
        this.baseURL = baseURL;
        this.cache = new Map();
        this.pendingRequests = new Map();
    }

    async request(url, options = {}) {
        const fullUrl = this.baseURL + url;
        const cacheKey = `${options.method || 'GET'}_${fullUrl}_${JSON.stringify(options.body || {})}`;
        
        // 检查缓存
        if (options.cache && this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < (options.cacheTime || 300000)) { // 5分钟缓存
                return cached.data;
            }
        }
        
        // 防止重复请求
        if (this.pendingRequests.has(cacheKey)) {
            return this.pendingRequests.get(cacheKey);
        }
        
        const requestPromise = fetch(fullUrl, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            // 缓存成功的GET请求
            if ((!options.method || options.method === 'GET') && options.cache) {
                this.cache.set(cacheKey, {
                    data,
                    timestamp: Date.now()
                });
            }
            return data;
        })
        .finally(() => {
            this.pendingRequests.delete(cacheKey);
        });
        
        this.pendingRequests.set(cacheKey, requestPromise);
        return requestPromise;
    }

    get(url, options = {}) {
        return this.request(url, { ...options, method: 'GET' });
    }

    post(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    put(url, data, options = {}) {
        return this.request(url, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    }

    clearCache() {
        this.cache.clear();
    }
}

// 全局API客户端
const api = new ApiClient();

// 通用分页组件
class PaginationComponent {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            maxVisiblePages: 5,
            onPageChange: () => {},
            ...options
        };
    }

    render(pagination) {
        const { current_page, total_pages, has_prev, has_next } = pagination;
        const { maxVisiblePages } = this.options;
        
        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${!has_prev ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${current_page - 1}" ${!has_prev ? 'tabindex="-1"' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码计算
        const startPage = Math.max(1, current_page - Math.floor(maxVisiblePages / 2));
        const endPage = Math.min(total_pages, startPage + maxVisiblePages - 1);
        
        // 第一页和省略号
        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        // 页码
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === current_page ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }
        
        // 最后一页和省略号
        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${total_pages}">${total_pages}</a></li>`;
        }
        
        // 下一页
        html += `
            <li class="page-item ${!has_next ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${current_page + 1}" ${!has_next ? 'tabindex="-1"' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        this.container.innerHTML = html;
        
        // 绑定事件
        this.container.addEventListener('click', (e) => {
            e.preventDefault();
            const page = parseInt(e.target.closest('a')?.dataset.page);
            if (page && page >= 1 && page <= total_pages) {
                this.options.onPageChange(page);
            }
        });
    }
}

// 通用表格组件
class DataTable {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            columns: [],
            data: [],
            selectable: false,
            sortable: true,
            onSort: () => {},
            onSelect: () => {},
            ...options
        };
        this.selectedRows = new Set();
        this.sortField = null;
        this.sortOrder = 'asc';
    }

    render(data = this.options.data) {
        const { columns, selectable } = this.options;
        
        let html = '<table class="table table-hover mb-0"><thead class="table-dark"><tr>';
        
        // 选择列
        if (selectable) {
            html += `
                <th width="40">
                    <input type="checkbox" class="form-check-input" id="selectAll">
                </th>
            `;
        }
        
        // 数据列
        columns.forEach(col => {
            const sortIcon = this.getSortIcon(col.field);
            html += `
                <th class="${col.sortable !== false ? 'sortable' : ''}" data-field="${col.field}">
                    ${col.title} ${sortIcon}
                </th>
            `;
        });
        
        html += '</tr></thead><tbody>';
        
        // 数据行
        if (data.length === 0) {
            html += `
                <tr>
                    <td colspan="${columns.length + (selectable ? 1 : 0)}" class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无数据</p>
                    </td>
                </tr>
            `;
        } else {
            data.forEach(row => {
                const isSelected = this.selectedRows.has(row.id);
                html += `<tr class="${isSelected ? 'selected-row' : ''}" data-id="${row.id}">`;
                
                if (selectable) {
                    html += `
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" 
                                   value="${row.id}" ${isSelected ? 'checked' : ''}>
                        </td>
                    `;
                }
                
                columns.forEach(col => {
                    const value = this.getCellValue(row, col);
                    html += `<td>${value}</td>`;
                });
                
                html += '</tr>';
            });
        }
        
        html += '</tbody></table>';
        this.container.innerHTML = html;
        
        this.bindEvents();
    }

    getCellValue(row, col) {
        const value = row[col.field];
        if (col.render) {
            return col.render(value, row);
        }
        return value || '';
    }

    getSortIcon(field) {
        if (this.sortField === field) {
            return this.sortOrder === 'asc' 
                ? '<i class="fas fa-sort-up text-success"></i>'
                : '<i class="fas fa-sort-down text-danger"></i>';
        }
        return '<i class="fas fa-sort text-muted"></i>';
    }

    bindEvents() {
        // 排序事件
        this.container.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', () => {
                const field = th.dataset.field;
                if (this.sortField === field) {
                    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
                } else {
                    this.sortField = field;
                    this.sortOrder = 'asc';
                }
                this.options.onSort(this.sortField, this.sortOrder);
            });
        });

        // 选择事件
        if (this.options.selectable) {
            const selectAll = this.container.querySelector('#selectAll');
            selectAll?.addEventListener('change', (e) => {
                const checked = e.target.checked;
                this.container.querySelectorAll('.row-checkbox').forEach(cb => {
                    cb.checked = checked;
                    const id = parseInt(cb.value);
                    if (checked) {
                        this.selectedRows.add(id);
                    } else {
                        this.selectedRows.delete(id);
                    }
                });
                this.options.onSelect(Array.from(this.selectedRows));
            });

            this.container.querySelectorAll('.row-checkbox').forEach(cb => {
                cb.addEventListener('change', (e) => {
                    const id = parseInt(e.target.value);
                    if (e.target.checked) {
                        this.selectedRows.add(id);
                    } else {
                        this.selectedRows.delete(id);
                    }
                    this.options.onSelect(Array.from(this.selectedRows));
                });
            });
        }
    }

    getSelectedRows() {
        return Array.from(this.selectedRows);
    }

    clearSelection() {
        this.selectedRows.clear();
        this.container.querySelectorAll('.row-checkbox').forEach(cb => {
            cb.checked = false;
        });
    }
}

// 通用加载状态
function showLoading(containerId, message = '加载中...') {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">${message}</span>
            </div>
            <p class="mt-2 mb-0">${message}</p>
        </div>
    `;
}

// 通用错误显示
function showError(containerId, message = '加载失败') {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="text-center py-4 text-danger">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <p>${message}</p>
            <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                <i class="fas fa-redo me-1"></i>重试
            </button>
        </div>
    `;
}

// 通用提示函数
function showAlert(message, type = 'info', duration = 5000) {
    const alertId = 'alert-' + Date.now();
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // 自动消失
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, duration);
}

// 导出工具函数
window.debounce = debounce;
window.throttle = throttle;
window.api = api;
window.PaginationComponent = PaginationComponent;
window.DataTable = DataTable;
window.showLoading = showLoading;
window.showError = showError;
window.showAlert = showAlert;
