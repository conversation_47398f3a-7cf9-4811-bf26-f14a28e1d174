{% extends "base.html" %}

{% block title %}系统设置 - 麻醉质控数据管理平台{% endblock %}
{% block page_title %}系统设置{% endblock %}

{% block content %}
<div class="row">
    <!-- 设置导航 -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    设置分类
                </h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                    <i class="fas fa-sliders-h me-2"></i>
                    常规设置
                </a>
                <a href="#privacy" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="fas fa-shield-alt me-2"></i>
                    隐私设置
                </a>
                <a href="#import" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="fas fa-upload me-2"></i>
                    导入设置
                </a>
                <a href="#database" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="fas fa-database me-2"></i>
                    数据库设置
                </a>
                <a href="#system" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="fas fa-server me-2"></i>
                    系统信息
                </a>
                <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="fas fa-save me-2"></i>
                    备份恢复
                </a>
            </div>
        </div>
    </div>
    
    <!-- 设置内容 -->
    <div class="col-md-9">
        <div class="tab-content">
            <!-- 常规设置 -->
            <div class="tab-pane fade show active" id="general">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sliders-h me-2"></i>
                            常规设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="generalSettingsForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="systemName" class="form-label">系统名称</label>
                                    <input type="text" class="form-control" id="systemName" value="麻醉质控数据管理平台">
                                </div>
                                <div class="col-md-6">
                                    <label for="systemVersion" class="form-label">系统版本</label>
                                    <input type="text" class="form-control" id="systemVersion" value="v2.0.0" readonly>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="defaultPageSize" class="form-label">默认分页大小</label>
                                    <select class="form-select" id="defaultPageSize">
                                        <option value="10">10</option>
                                        <option value="25" selected>25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="dateFormat" class="form-label">日期格式</label>
                                    <select class="form-select" id="dateFormat">
                                        <option value="YYYY-MM-DD" selected>YYYY-MM-DD</option>
                                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="sessionTimeout" class="form-label">会话超时(分钟)</label>
                                    <input type="number" class="form-control" id="sessionTimeout" value="30" min="5" max="480">
                                </div>
                                <div class="col-md-6">
                                    <label for="logLevel" class="form-label">日志级别</label>
                                    <select class="form-select" id="logLevel">
                                        <option value="DEBUG">DEBUG</option>
                                        <option value="INFO" selected>INFO</option>
                                        <option value="WARNING">WARNING</option>
                                        <option value="ERROR">ERROR</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                    <label class="form-check-label" for="enableNotifications">
                                        启用系统通知
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                保存设置
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 隐私设置 -->
            <div class="tab-pane fade" id="privacy">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            隐私设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="privacySettingsForm">
                            <h6 class="mb-3">默认脱敏选项</h6>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="defaultAnonymizeNames" checked>
                                        <label class="form-check-label" for="defaultAnonymizeNames">
                                            默认脱敏患者姓名
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="defaultAnonymizeIds" checked>
                                        <label class="form-check-label" for="defaultAnonymizeIds">
                                            默认脱敏患者ID
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="defaultMaskPhone" checked>
                                        <label class="form-check-label" for="defaultMaskPhone">
                                            默认掩码手机号
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="defaultAnonymizeDoctors">
                                        <label class="form-check-label" for="defaultAnonymizeDoctors">
                                            默认脱敏医生姓名
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <h6 class="mb-3 mt-4">数据保留策略</h6>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="dataRetentionDays" class="form-label">数据保留天数</label>
                                    <input type="number" class="form-control" id="dataRetentionDays" value="365" min="30">
                                    <div class="form-text">超过此天数的数据将被自动清理</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="logRetentionDays" class="form-label">日志保留天数</label>
                                    <input type="number" class="form-control" id="logRetentionDays" value="90" min="7">
                                    <div class="form-text">超过此天数的日志将被自动清理</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableAutoCleanup" checked>
                                    <label class="form-check-label" for="enableAutoCleanup">
                                        启用自动清理
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                保存设置
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 导入设置 -->
            <div class="tab-pane fade" id="import">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-upload me-2"></i>
                            导入设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="importSettingsForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="maxFileSize" class="form-label">最大文件大小 (MB)</label>
                                    <input type="number" class="form-control" id="maxFileSize" value="50" min="1" max="500">
                                </div>
                                <div class="col-md-6">
                                    <label for="allowedExtensions" class="form-label">允许的文件扩展名</label>
                                    <input type="text" class="form-control" id="allowedExtensions" value=".xlsx,.xls" readonly>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="duplicateHandling" class="form-label">重复数据处理</label>
                                    <select class="form-select" id="duplicateHandling">
                                        <option value="skip" selected>跳过重复数据</option>
                                        <option value="update">更新重复数据</option>
                                        <option value="error">报错停止</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="batchSize" class="form-label">批处理大小</label>
                                    <input type="number" class="form-control" id="batchSize" value="1000" min="100" max="10000">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableValidation" checked>
                                    <label class="form-check-label" for="enableValidation">
                                        启用数据验证
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableAutoBackup" checked>
                                    <label class="form-check-label" for="enableAutoBackup">
                                        导入前自动备份
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                保存设置
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 数据库设置 -->
            <div class="tab-pane fade" id="database">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>
                            数据库设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>数据库信息</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>数据库类型:</strong></td><td>SQLite</td></tr>
                                    <tr><td><strong>数据库文件:</strong></td><td>data/anesthesia_qc.db</td></tr>
                                    <tr><td><strong>文件大小:</strong></td><td id="dbFileSize">-</td></tr>
                                    <tr><td><strong>创建时间:</strong></td><td id="dbCreateTime">-</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>表统计</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>患者表:</strong></td><td id="patientsCount">-</td></tr>
                                    <tr><td><strong>手术记录表:</strong></td><td id="surgeryRecordsCount">-</td></tr>
                                    <tr><td><strong>导入批次表:</strong></td><td id="importBatchesCount">-</td></tr>
                                    <tr><td><strong>操作日志表:</strong></td><td id="operationLogsCount">-</td></tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <h6>数据库操作</h6>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="optimizeDatabase()">
                                        <i class="fas fa-tools me-1"></i>
                                        优化数据库
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="repairDatabase()">
                                        <i class="fas fa-wrench me-1"></i>
                                        修复数据库
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="analyzeDatabase()">
                                        <i class="fas fa-chart-bar me-1"></i>
                                        分析数据库
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="tab-pane fade" id="system">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-server me-2"></i>
                            系统信息
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshSystemInfo()">
                            <i class="fas fa-sync-alt me-1"></i>
                            刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="progress mb-2" style="height: 10px;">
                                        <div class="progress-bar bg-primary" id="cpuProgress" style="width: 0%"></div>
                                    </div>
                                    <h6>CPU使用率</h6>
                                    <h4 id="cpuUsage" class="text-primary">-%</h4>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="progress mb-2" style="height: 10px;">
                                        <div class="progress-bar bg-success" id="memoryProgress" style="width: 0%"></div>
                                    </div>
                                    <h6>内存使用率</h6>
                                    <h4 id="memoryUsage" class="text-success">-%</h4>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="progress mb-2" style="height: 10px;">
                                        <div class="progress-bar bg-warning" id="diskProgress" style="width: 0%"></div>
                                    </div>
                                    <h6>磁盘使用率</h6>
                                    <h4 id="diskUsage" class="text-warning">-%</h4>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>系统环境</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>操作系统:</strong></td><td id="osInfo">-</td></tr>
                                    <tr><td><strong>Python版本:</strong></td><td id="pythonVersion">-</td></tr>
                                    <tr><td><strong>Flask版本:</strong></td><td id="flaskVersion">-</td></tr>
                                    <tr><td><strong>启动时间:</strong></td><td id="startTime">-</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>网络信息</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>服务地址:</strong></td><td>http://127.0.0.1:5000</td></tr>
                                    <tr><td><strong>当前连接:</strong></td><td id="activeConnections">-</td></tr>
                                    <tr><td><strong>总请求数:</strong></td><td id="totalRequests">-</td></tr>
                                    <tr><td><strong>运行时长:</strong></td><td id="uptime">-</td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 备份恢复 -->
            <div class="tab-pane fade" id="backup">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-save me-2"></i>
                            备份恢复
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>创建备份</h6>
                                <p class="text-muted">备份当前数据库和配置文件</p>
                                <div class="mb-3">
                                    <label for="backupName" class="form-label">备份名称</label>
                                    <input type="text" class="form-control" id="backupName" placeholder="输入备份名称...">
                                </div>
                                <button class="btn btn-success" onclick="createBackup()">
                                    <i class="fas fa-save me-1"></i>
                                    创建备份
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>恢复备份</h6>
                                <p class="text-muted">从备份文件恢复数据</p>
                                <div class="mb-3">
                                    <label for="backupFile" class="form-label">选择备份文件</label>
                                    <input type="file" class="form-control" id="backupFile" accept=".zip,.sql">
                                </div>
                                <button class="btn btn-warning" onclick="restoreBackup()">
                                    <i class="fas fa-upload me-1"></i>
                                    恢复备份
                                </button>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6>备份历史</h6>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>备份名称</th>
                                                <th>创建时间</th>
                                                <th>文件大小</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="backupHistoryTable">
                                            <tr>
                                                <td>auto_backup_20250112</td>
                                                <td>2025-01-12 10:30:00</td>
                                                <td>2.5 MB</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary me-1">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 绑定表单提交事件
    document.getElementById('generalSettingsForm').addEventListener('submit', handleGeneralSettings);
    document.getElementById('privacySettingsForm').addEventListener('submit', handlePrivacySettings);
    document.getElementById('importSettingsForm').addEventListener('submit', handleImportSettings);
    
    // 加载系统信息
    loadSystemInfo();
    loadDatabaseInfo();
    
    // 定时刷新系统信息
    setInterval(loadSystemInfo, 30000); // 每30秒刷新一次
});

function handleGeneralSettings(event) {
    event.preventDefault();
    showAlert('常规设置已保存', 'success');
}

function handlePrivacySettings(event) {
    event.preventDefault();
    showAlert('隐私设置已保存', 'success');
}

function handleImportSettings(event) {
    event.preventDefault();
    showAlert('导入设置已保存', 'success');
}

function loadSystemInfo() {
    fetch('/api/system-info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSystemInfo(data.data);
            }
        })
        .catch(error => {
            console.error('获取系统信息失败:', error);
        });
}

function updateSystemInfo(info) {
    // 更新CPU使用率
    const cpuUsage = info.cpu_usage || 0;
    document.getElementById('cpuUsage').textContent = cpuUsage.toFixed(1) + '%';
    document.getElementById('cpuProgress').style.width = cpuUsage + '%';
    
    // 更新内存使用率
    const memoryUsage = info.memory_usage || 0;
    document.getElementById('memoryUsage').textContent = memoryUsage.toFixed(1) + '%';
    document.getElementById('memoryProgress').style.width = memoryUsage + '%';
    
    // 更新磁盘使用率
    const diskUsage = info.disk_usage || 0;
    document.getElementById('diskUsage').textContent = diskUsage.toFixed(1) + '%';
    document.getElementById('diskProgress').style.width = diskUsage + '%';
    
    // 更新其他信息
    document.getElementById('osInfo').textContent = 'Windows 11';
    document.getElementById('pythonVersion').textContent = '3.8+';
    document.getElementById('flaskVersion').textContent = '2.3.0';
    document.getElementById('startTime').textContent = new Date().toLocaleString();
    document.getElementById('activeConnections').textContent = '1';
    document.getElementById('totalRequests').textContent = '156';
    document.getElementById('uptime').textContent = '2小时15分钟';
}

function loadDatabaseInfo() {
    // 模拟数据库信息
    document.getElementById('dbFileSize').textContent = '15.2 MB';
    document.getElementById('dbCreateTime').textContent = '2025-01-01 00:00:00';
    document.getElementById('patientsCount').textContent = '1,250';
    document.getElementById('surgeryRecordsCount').textContent = '3,680';
    document.getElementById('importBatchesCount').textContent = '25';
    document.getElementById('operationLogsCount').textContent = '1,856';
}

function refreshSystemInfo() {
    showAlert('正在刷新系统信息...', 'info');
    loadSystemInfo();
    setTimeout(() => {
        showAlert('系统信息已更新', 'success');
    }, 1000);
}

function optimizeDatabase() {
    if (confirm('确定要优化数据库吗？这可能需要几分钟时间。')) {
        showAlert('正在优化数据库...', 'info');
        setTimeout(() => {
            showAlert('数据库优化完成！', 'success');
        }, 3000);
    }
}

function repairDatabase() {
    if (confirm('确定要修复数据库吗？建议先创建备份。')) {
        showAlert('正在修复数据库...', 'warning');
        setTimeout(() => {
            showAlert('数据库修复完成！', 'success');
        }, 2000);
    }
}

function analyzeDatabase() {
    showAlert('正在分析数据库...', 'info');
    setTimeout(() => {
        showAlert('数据库分析完成！详细报告已生成。', 'success');
    }, 2000);
}

function createBackup() {
    const backupName = document.getElementById('backupName').value;
    if (!backupName) {
        showAlert('请输入备份名称', 'warning');
        return;
    }
    
    showAlert('正在创建备份...', 'info');
    setTimeout(() => {
        showAlert('备份创建成功！', 'success');
        document.getElementById('backupName').value = '';
    }, 3000);
}

function restoreBackup() {
    const backupFile = document.getElementById('backupFile').files[0];
    if (!backupFile) {
        showAlert('请选择备份文件', 'warning');
        return;
    }
    
    if (confirm('确定要恢复备份吗？当前数据将被覆盖！')) {
        showAlert('正在恢复备份...', 'warning');
        setTimeout(() => {
            showAlert('备份恢复成功！', 'success');
            document.getElementById('backupFile').value = '';
        }, 5000);
    }
}
</script>
{% endblock %}
